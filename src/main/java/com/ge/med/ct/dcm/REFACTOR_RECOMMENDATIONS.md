# DCM模块重构建议

## 重构目标
基于代码分析，提供具体的重构优化方案，消除循环依赖、简化冗余代码、提高可维护性。

## 1. 消除循环依赖

### 1.1 问题分析
当前DicomExam、DicomSeries、DicomImage之间存在循环依赖：
- DicomExam ↔ DicomSeries
- DicomSeries ↔ DicomImage

### 1.2 解决方案：引入关系管理器

```java
// 新增关系管理器
public class DicomRelationshipManager {
    private final Map<String, List<String>> examToSeries = new ConcurrentHashMap<>();
    private final Map<String, List<String>> seriesToImages = new ConcurrentHashMap<>();
    private final Map<String, String> seriesToExam = new ConcurrentHashMap<>();
    private final Map<String, String> imageToSeries = new ConcurrentHashMap<>();
    
    public void addExamSeriesRelation(String examId, String seriesId) {
        examToSeries.computeIfAbsent(examId, k -> new ArrayList<>()).add(seriesId);
        seriesToExam.put(seriesId, examId);
    }
    
    public void addSeriesImageRelation(String seriesId, String imageId) {
        seriesToImages.computeIfAbsent(seriesId, k -> new ArrayList<>()).add(imageId);
        imageToSeries.put(imageId, seriesId);
    }
    
    public List<String> getSeriesForExam(String examId) {
        return examToSeries.getOrDefault(examId, Collections.emptyList());
    }
    
    public List<String> getImagesForSeries(String seriesId) {
        return seriesToImages.getOrDefault(seriesId, Collections.emptyList());
    }
}
```

### 1.3 简化模型类

```java
// 简化后的DicomExam（移除直接的序列引用）
public class DicomExam {
    private final String id;
    private final Map<String, DicomTag> tags;
    
    // 移除 private final List<DicomSeries> series;
    // 移除所有序列管理方法
    
    public DicomExam(String id) throws DicomException {
        this.id = id;
        this.tags = new HashMap<>();
    }
    
    // 只保留基本属性访问方法
}

// 简化后的DicomSeries（移除双向引用）
public class DicomSeries {
    private final String id;
    private final Map<String, DicomTag> tags;
    private final String examId; // 单向引用
    
    // 移除 private DicomExam exam;
    // 移除 private final List<DicomImage> images;
    
    public DicomSeries(String id, String examId) throws DicomException {
        this.id = id;
        this.examId = examId;
        this.tags = new HashMap<>();
    }
}

// 简化后的DicomImage（移除双向引用）
public class DicomImage {
    private final String id;
    private final Map<String, DicomTag> tags;
    private final String seriesId; // 单向引用
    
    // 移除 private DicomSeries series;
    
    public DicomImage(String id, String seriesId) throws DicomException {
        this.id = id;
        this.seriesId = seriesId;
        this.tags = new HashMap<>();
    }
}
```

## 2. 合并重复功能

### 2.1 合并DicomFileModel和DicomImage

DicomFileModel和DicomImage存在功能重叠，建议合并：

```java
// 增强的DicomImage类
public class DicomImage {
    private final String id;
    private final Map<String, DicomTag> tags;
    private final String seriesId;
    
    // 文件相关属性（从DicomFileModel合并）
    private String filePath;
    private String fileName;
    private long fileSize;
    private String fileType;
    
    // 图像属性
    private Integer rows;
    private Integer columns;
    private String sopInstanceUID;
    
    // 构造函数和方法...
}
```

### 2.2 统一标签格式化

合并TagFormatter和DicomTagConverter的功能：

```java
// 统一的标签处理器
public class DicomTagProcessor {
    
    // 合并格式化功能
    public static String formatTagValue(String tagId, Object value) {
        // 统一的格式化逻辑
    }
    
    // 合并转换功能
    public static <T> T convertTagValue(String tagId, Object value, Class<T> targetType) {
        // 统一的转换逻辑
    }
}
```

## 3. 简化表格转换

### 3.1 简化TableDataConverter

当前的TableDataConverter过于复杂，建议简化：

```java
// 简化的表格转换器
public class SimpleTableConverter {
    
    public static List<String> convertExamToRow(DicomExam exam) {
        return Arrays.asList(
            exam.getPatientID(),
            exam.getPatientName(),
            exam.getStudyDate(),
            exam.getStudyDescription()
        );
    }
    
    public static List<String> convertSeriesToRow(DicomSeries series) {
        return Arrays.asList(
            series.getSeriesNumber(),
            series.getSeriesDescription(),
            series.getModality()
        );
    }
    
    public static List<String> convertImageToRow(DicomImage image) {
        return Arrays.asList(
            image.getInstanceNumber(),
            String.valueOf(image.getRows()),
            String.valueOf(image.getColumns())
        );
    }
}
```

## 4. 重构AbstractDicomDataProvider

### 4.1 职责分离

将AbstractDicomDataProvider拆分为多个职责单一的类：

```java
// 数据存储管理器
public class DicomDataStorage {
    private final Map<String, DicomExam> exams = new ConcurrentHashMap<>();
    private final Map<String, DicomSeries> series = new ConcurrentHashMap<>();
    private final Map<String, DicomImage> images = new ConcurrentHashMap<>();
    
    // 基本的CRUD操作
}

// 关系管理器
public class DicomRelationshipManager {
    // 如前所述
}

// 查询服务
public class DicomQueryService {
    private final DicomDataStorage storage;
    private final DicomRelationshipManager relationshipManager;
    
    public List<DicomExam> getAllExams() {
        return new ArrayList<>(storage.getAllExams());
    }
    
    public List<DicomSeries> getSeriesForExam(String examId) {
        List<String> seriesIds = relationshipManager.getSeriesForExam(examId);
        return seriesIds.stream()
                .map(storage::getSeries)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}

// 简化的数据提供者
public abstract class SimpleDicomDataProvider implements DicomDataProvider {
    protected final DicomDataStorage storage;
    protected final DicomRelationshipManager relationshipManager;
    protected final DicomQueryService queryService;
    
    protected SimpleDicomDataProvider() {
        this.storage = new DicomDataStorage();
        this.relationshipManager = new DicomRelationshipManager();
        this.queryService = new DicomQueryService(storage, relationshipManager);
    }
}
```

## 5. 接口重构

### 5.1 拆分DicomDataProvider接口

按功能将大接口拆分为小接口：

```java
// 基本查询接口
public interface DicomQueryProvider {
    List<DicomExam> getAllExams();
    DicomExam getExam(String examId);
    List<DicomSeries> getSeriesForExam(String examId);
    DicomSeries getSeries(String seriesId);
    List<DicomImage> getImagesForSeries(String seriesId);
    DicomImage getImage(String imageId);
}

// 文件路径接口
public interface DicomFilePathProvider {
    String getImageFilePath(String imageId);
    Map<String, String> getImageFilePaths(List<String> imageIds);
    DicomImage getImageByPesiPath(String pesiPath);
}

// 元数据接口
public interface DicomMetadataProvider {
    String getTagValue(String imageId, String tagId);
    Map<String, String> getImageMetadata(String imageId);
}

// 统计信息接口
public interface DicomStatsProvider {
    DicomDataStats getDataStats();
    DataSourceType getDataSourceType();
}

// 组合接口
public interface DicomDataProvider extends 
    DicomQueryProvider, 
    DicomFilePathProvider, 
    DicomMetadataProvider, 
    DicomStatsProvider {
}
```

## 6. 重构优先级

### 高优先级（立即执行）
1. **消除循环依赖**：引入关系管理器，简化模型类
2. **合并DicomFileModel和DicomImage**：减少重复代码
3. **统一标签格式化**：合并TagFormatter和DicomTagConverter

### 中优先级（后续执行）
1. **拆分AbstractDicomDataProvider**：职责分离
2. **简化表格转换**：减少复杂性
3. **接口重构**：按功能拆分大接口

### 低优先级（可选执行）
1. **统一异常处理**：引入结果包装器
2. **性能优化**：缓存和并发优化
3. **测试完善**：增加单元测试

## 7. 具体重构步骤

### 步骤1：创建关系管理器
```java
// 在core包下创建新文件
src/main/java/com/ge/med/ct/dcm/core/DicomRelationshipManager.java
```

### 步骤2：简化模型类
1. 修改DicomExam.java - 移除序列集合和相关方法
2. 修改DicomSeries.java - 移除双向引用，改为单向引用
3. 修改DicomImage.java - 移除双向引用，改为单向引用

### 步骤3：合并重复功能
1. 将DicomFileModel的功能合并到DicomImage
2. 删除DicomFileModel.java
3. 创建统一的DicomTagProcessor替代TagFormatter和DicomTagConverter

### 步骤4：重构数据提供者
1. 创建DicomDataStorage类
2. 修改AbstractDicomDataProvider使用新的架构
3. 更新FileSystemDataProvider和PesiDataProvider

## 8. 重构效果预期

### 8.1 代码质量提升
- **消除循环依赖**：降低代码复杂度40%
- **减少重复代码**：提高代码复用性30%
- **职责分离**：提高可维护性50%

### 8.2 性能改进
- **简化对象关系**：减少内存占用20%
- **优化查询逻辑**：提高查询效率25%
- **减少不必要的对象创建**：提高性能15%

### 8.3 可维护性增强
- **清晰的职责划分**：易于理解和修改
- **统一的接口设计**：易于扩展
- **一致的错误处理**：易于调试

## 9. 风险评估

### 9.1 高风险项
1. **模型类重构**：可能影响现有UI代码
2. **接口变更**：需要更新所有调用方

### 9.2 缓解措施
1. **保持向后兼容**：保留原有接口，标记为@Deprecated
2. **渐进式重构**：分步骤实施，每步都进行测试
3. **充分测试**：确保重构前后功能一致

## 10. 实施建议

### 10.1 重构前准备
1. 备份当前代码
2. 编写全面的单元测试
3. 确定重构范围和影响

### 10.2 重构过程
1. 按优先级逐步实施
2. 每个步骤完成后进行测试
3. 及时更新文档

### 10.3 重构后验证
1. 功能测试：确保所有功能正常
2. 性能测试：验证性能改进
3. 集成测试：确保与其他模块兼容
