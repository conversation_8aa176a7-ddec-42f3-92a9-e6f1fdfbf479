package com.ge.med.ct.dicom.model;

import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;

import java.util.*;
import java.util.logging.Logger;

/**
 * DICOM序列类（重构版）
 * 移除了与检查和图像的直接关联，消除循环依赖
 * 使用单向引用到检查ID，关系管理由DicomRelationshipManager负责
 */
public class DicomSeries {
    private static final Logger LOG = Logger.getLogger(DicomSeries.class.getName());

    private final String id;
    private final String examId; // 单向引用到检查ID
    private final Map<String, DicomTag> tags;

    // 常用属性缓存
    private String seriesInstanceUID;
    private String seriesNumber;
    private String seriesDescription;
    private String modality;
    private String seriesDate;
    private String seriesTime;

    public DicomSeries(String id, String examId) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException(DicomMessages.SERIES_ID_EMPTY);
        }
        if (examId == null || examId.trim().isEmpty()) {
            throw new DicomException("检查ID不能为空");
        }
        
        this.id = id;
        this.examId = examId;
        this.tags = new HashMap<>();
        LOG.fine("创建DICOM序列: " + id + " (检查: " + examId + ")");
    }

    // === 基本属性访问 ===

    public String getId() {
        return id;
    }

    public String getExamId() {
        return examId;
    }

    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }

    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            updateCachedAttributes(tagId, tag.getValueAsString());
            LOG.fine("添加标签 " + tagId + " 到序列 " + id);
        }
    }

    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }

    public Map<String, DicomTag> getTags() {
        return Collections.unmodifiableMap(tags);
    }

    // === 序列信息 ===

    public String getSeriesInstanceUID() {
        return seriesInstanceUID != null ? seriesInstanceUID : getTagValue("(0020,000E)");
    }

    public void setSeriesInstanceUID(String seriesInstanceUID) {
        this.seriesInstanceUID = seriesInstanceUID;
        setTagValue("(0020,000E)", seriesInstanceUID);
    }

    public String getSeriesNumber() {
        return seriesNumber != null ? seriesNumber : getTagValue("(0020,0011)");
    }

    public void setSeriesNumber(String seriesNumber) {
        this.seriesNumber = seriesNumber;
        setTagValue("(0020,0011)", seriesNumber);
    }

    public String getSeriesDescription() {
        return seriesDescription != null ? seriesDescription : getTagValue("(0008,103E)");
    }

    public void setSeriesDescription(String seriesDescription) {
        this.seriesDescription = seriesDescription;
        setTagValue("(0008,103E)", seriesDescription);
    }

    public String getModality() {
        return modality != null ? modality : getTagValue("(0008,0060)");
    }

    public void setModality(String modality) {
        this.modality = modality;
        setTagValue("(0008,0060)", modality);
    }

    public String getSeriesDate() {
        return seriesDate != null ? seriesDate : getTagValue("(0008,0021)");
    }

    public void setSeriesDate(String seriesDate) {
        this.seriesDate = seriesDate;
        setTagValue("(0008,0021)", seriesDate);
    }

    public String getSeriesTime() {
        return seriesTime != null ? seriesTime : getTagValue("(0008,0031)");
    }

    public void setSeriesTime(String seriesTime) {
        this.seriesTime = seriesTime;
        setTagValue("(0008,0031)", seriesTime);
    }

    public String getBodyPartExamined() {
        return getTagValue("(0018,0015)");
    }

    public void setBodyPartExamined(String bodyPart) {
        setTagValue("(0018,0015)", bodyPart);
    }

    public String getOperatorName() {
        return getTagValue("(0008,1070)");
    }

    public void setOperatorName(String operatorName) {
        setTagValue("(0008,1070)", operatorName);
    }

    // === 工具方法 ===

    private void setTagValue(String tagId, String value) {
        if (value != null) {
            try {
                DicomTag tag = new DicomTag(tagId, value, org.dcm4che3.data.VR.LO);
                addTag(tagId, tag);
            } catch (DicomException e) {
                LOG.warning("设置标签值失败: " + tagId + " = " + value);
            }
        }
    }

    private void updateCachedAttributes(String tagId, String value) {
        switch (tagId) {
            case "(0020,000E)":
                this.seriesInstanceUID = value;
                break;
            case "(0020,0011)":
                this.seriesNumber = value;
                break;
            case "(0008,103E)":
                this.seriesDescription = value;
                break;
            case "(0008,0060)":
                this.modality = value;
                break;
            case "(0008,0021)":
                this.seriesDate = value;
                break;
            case "(0008,0031)":
                this.seriesTime = value;
                break;
        }
    }

    /**
     * 验证序列数据的完整性
     */
    public boolean isValid() {
        return id != null && !id.trim().isEmpty() &&
               examId != null && !examId.trim().isEmpty() &&
               getSeriesInstanceUID() != null && !getSeriesInstanceUID().trim().isEmpty() &&
               getModality() != null && !getModality().trim().isEmpty();
    }

    /**
     * 获取序列的显示名称
     */
    public String getDisplayName() {
        String number = getSeriesNumber();
        String description = getSeriesDescription();
        String modality = getModality();
        
        StringBuilder displayName = new StringBuilder();
        if (number != null && !number.isEmpty()) {
            displayName.append("Series ").append(number);
        }
        if (modality != null && !modality.isEmpty()) {
            if (displayName.length() > 0) displayName.append(" - ");
            displayName.append(modality);
        }
        if (description != null && !description.isEmpty()) {
            if (displayName.length() > 0) displayName.append(" - ");
            displayName.append(description);
        }
        
        return displayName.length() > 0 ? displayName.toString() : id;
    }

    @Override
    public String toString() {
        return String.format("DicomSeries[id=%s, examId=%s, seriesNumber=%s, modality=%s, description=%s]",
                id, examId, getSeriesNumber(), getModality(), getSeriesDescription());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        DicomSeries other = (DicomSeries) obj;
        return id.equals(other.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
