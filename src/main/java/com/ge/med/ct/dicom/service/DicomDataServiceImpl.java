package com.ge.med.ct.dicom.service;

import com.ge.med.ct.dicom.provider.DicomDataProvider;
import com.ge.med.ct.dicom.provider.FileSystemDataProvider;
import com.ge.med.ct.dicom.provider.PesiDataProvider;
import com.ge.med.ct.dicom.source.DicomDataSource;
import com.ge.med.ct.dicom.source.FileSystemDataSource;
import com.ge.med.ct.dicom.source.PesiDataSource;
import com.ge.med.ct.dicom.query.DicomQueryService;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Logger;

/**
 * DICOM数据服务实现（重构版）
 * 单例模式，管理数据加载和提供者
 */
public class DicomDataServiceImpl implements DicomDataService {
    private static final Logger LOG = Logger.getLogger(DicomDataServiceImpl.class.getName());

    // 单例实例
    private static volatile DicomDataServiceImpl instance;
    private static final Object lock = new Object();

    // 状态管理
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final AtomicBoolean loading = new AtomicBoolean(false);
    private final AtomicReference<DicomDataProvider> dataProvider = new AtomicReference<>();
    private final AtomicReference<DicomDataSource> currentDataSource = new AtomicReference<>();
    private final AtomicReference<ProgressCallback> currentCallback = new AtomicReference<>();

    // 数据提供者实例
    private FileSystemDataProvider fileSystemProvider;
    private PesiDataProvider pesiProvider;

    private DicomDataServiceImpl() {
        LOG.info("初始化DICOM数据服务");
    }

    /**
     * 获取单例实例
     */
    public static DicomDataServiceImpl getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new DicomDataServiceImpl();
                }
            }
        }
        return instance;
    }

    // === DicomDataService接口实现 ===

    @Override
    public void loadDicomData(DicomDataSource source, ProgressCallback callback) {
        if (source == null) {
            throw new IllegalArgumentException("数据源不能为空");
        }

        if (!source.isValid()) {
            throw new IllegalArgumentException("数据源配置无效: " + source.getConfigSummary());
        }

        if (loading.get()) {
            LOG.warning("数据正在加载中，忽略新的加载请求");
            return;
        }

        loading.set(true);
        currentDataSource.set(source);
        currentCallback.set(callback);

        try {
            LOG.info("开始加载DICOM数据: " + source.getDescription());

            if (callback != null) {
                callback.onProgress(new ProgressInfo("初始化数据加载...", 0));
            }

            switch (source.getType()) {
                case FILE_SYSTEM:
                    loadFromFileSystem((FileSystemDataSource) source, callback);
                    break;

                case PESI:
                    loadFromPesi((PesiDataSource) source, callback);
                    break;

                default:
                    throw new IllegalArgumentException("不支持的数据源类型: " + source.getType());
            }

            initialized.set(true);
            LOG.info("DICOM数据加载完成: " + source.getType());

            if (callback != null) {
                callback.onComplete();
            }

        } catch (Exception e) {
            String error = "加载DICOM数据失败: " + e.getMessage();
            LOG.severe(error);

            if (callback != null) {
                callback.onError(error);
            }

            // 清理状态
            dataProvider.set(null);
            initialized.set(false);

        } finally {
            loading.set(false);
        }
    }

    @Override
    public DicomDataProvider getDataProvider() {
        DicomDataProvider provider = dataProvider.get();
        if (provider == null) {
            throw new IllegalStateException("数据提供者未初始化，请先加载数据");
        }
        return provider;
    }

    @Override
    public DicomDataProvider.DataSourceType getCurrentSourceType() {
        DicomDataProvider provider = dataProvider.get();
        return provider != null ? provider.getDataSourceType() : null;
    }

    @Override
    public void refreshData() {
        DicomDataSource source = currentDataSource.get();
        ProgressCallback callback = currentCallback.get();

        if (source == null) {
            throw new IllegalStateException("没有可刷新的数据源");
        }

        LOG.info("刷新DICOM数据");
        loadDicomData(source, callback);
    }

    @Override
    public DicomQueryService.DataStats getDataStats() {
        DicomDataProvider provider = dataProvider.get();
        if (provider == null) {
            return new DicomQueryService.DataStats(0, 0, 0, 0, 0, 0, 0);
        }
        return provider.getDataStats();
    }

    @Override
    public boolean isInitialized() {
        return initialized.get();
    }

    @Override
    public boolean isLoading() {
        return loading.get();
    }

    @Override
    public void close() {
        LOG.info("关闭DICOM数据服务");

        // 清理数据提供者
        DicomDataProvider provider = dataProvider.get();
        if (provider != null) {
            provider.clearData();
        }

        // 重置状态
        initialized.set(false);
        loading.set(false);
        dataProvider.set(null);
        currentDataSource.set(null);
        currentCallback.set(null);

        // 清理提供者实例
        fileSystemProvider = null;
        pesiProvider = null;
    }

    // === 私有方法 ===

    /**
     * 从文件系统加载数据
     */
    private void loadFromFileSystem(FileSystemDataSource source, ProgressCallback callback) {
        LOG.info("从文件系统加载DICOM数据: " + source.getRootDirectory());

        if (callback != null) {
            callback.onProgress(new ProgressInfo("初始化文件系统扫描器...", 10));
        }

        try {
            // 创建或重用文件系统提供者
            if (fileSystemProvider == null) {
                fileSystemProvider = new FileSystemDataProvider();
            }

            // 加载数据
            fileSystemProvider.loadFromFileSystem(source);
            dataProvider.set(fileSystemProvider);

            if (callback != null) {
                callback.onProgress(new ProgressInfo("文件系统数据加载完成", 100));
            }

        } catch (Exception e) {
            throw new RuntimeException("文件系统数据加载失败", e);
        }
    }

    /**
     * 从PESI加载数据
     */
    private void loadFromPesi(PesiDataSource source, ProgressCallback callback) {
        LOG.info("从PESI数据库加载DICOM数据: " + source.getExecuteQueryScript());

        if (callback != null) {
            callback.onProgress(new ProgressInfo("初始化PESI查询服务...", 10));
        }

        try {
            // 创建或重用PESI提供者
            if (pesiProvider == null) {
                pesiProvider = new PesiDataProvider();
            }

            // 加载数据
            pesiProvider.loadFromPesi(source);
            dataProvider.set(pesiProvider);

            if (callback != null) {
                callback.onProgress(new ProgressInfo("PESI数据加载完成", 100));
            }

        } catch (Exception e) {
            throw new RuntimeException("PESI数据加载失败", e);
        }
    }

    /**
     * 获取服务状态信息
     */
    public String getServiceStatus() {
        StringBuilder status = new StringBuilder();
        status.append("DicomDataService状态:\n");
        status.append("- 已初始化: ").append(initialized.get()).append("\n");
        status.append("- 正在加载: ").append(loading.get()).append("\n");
        status.append("- 数据源类型: ").append(getCurrentSourceType()).append("\n");

        if (initialized.get()) {
            DicomQueryService.DataStats stats = getDataStats();
            status.append("- 数据统计: ").append(stats).append("\n");
        }

        return status.toString();
    }
}
