package com.ge.med.ct.dicom.core;

import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * 文件系统数据源配置
 * 配置从文件系统加载DICOM数据的参数
 */
public class FileSystemDataSource implements DicomDataSource {
    
    private String rootDirectory;
    private boolean recursive = true;
    private List<String> fileExtensions = Arrays.asList(".dcm", ".dicom", "");
    private boolean includeSubdirectories = true;
    private int maxFiles = -1; // -1表示无限制
    
    public FileSystemDataSource(String rootDirectory) {
        this.rootDirectory = rootDirectory;
    }
    
    // === DicomDataSource接口实现 ===
    
    @Override
    public DicomDataProvider.DataSourceType getType() {
        return DicomDataProvider.DataSourceType.FILE_SYSTEM;
    }
    
    @Override
    public String getDescription() {
        return "文件系统数据源: " + rootDirectory;
    }
    
    @Override
    public boolean isValid() {
        if (rootDirectory == null || rootDirectory.trim().isEmpty()) {
            return false;
        }
        
        File dir = new File(rootDirectory);
        return dir.exists() && dir.isDirectory() && dir.canRead();
    }
    
    @Override
    public String getConfigSummary() {
        return String.format("FileSystem[root=%s, recursive=%s, extensions=%s, maxFiles=%d]",
                rootDirectory, recursive, fileExtensions, maxFiles);
    }
    
    // === Getter和Setter方法 ===
    
    public String getRootDirectory() {
        return rootDirectory;
    }
    
    public void setRootDirectory(String rootDirectory) {
        this.rootDirectory = rootDirectory;
    }
    
    public boolean isRecursive() {
        return recursive;
    }
    
    public void setRecursive(boolean recursive) {
        this.recursive = recursive;
    }
    
    public List<String> getFileExtensions() {
        return fileExtensions;
    }
    
    public void setFileExtensions(List<String> fileExtensions) {
        this.fileExtensions = fileExtensions;
    }
    
    public boolean isIncludeSubdirectories() {
        return includeSubdirectories;
    }
    
    public void setIncludeSubdirectories(boolean includeSubdirectories) {
        this.includeSubdirectories = includeSubdirectories;
    }
    
    public int getMaxFiles() {
        return maxFiles;
    }
    
    public void setMaxFiles(int maxFiles) {
        this.maxFiles = maxFiles;
    }
    
    // === 工具方法 ===
    
    /**
     * 检查文件是否符合扩展名要求
     */
    public boolean isValidFileExtension(String fileName) {
        if (fileName == null) {
            return false;
        }
        
        String lowerFileName = fileName.toLowerCase();
        for (String ext : fileExtensions) {
            if (ext.isEmpty() || lowerFileName.endsWith(ext.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 创建默认的文件系统数据源
     */
    public static FileSystemDataSource createDefault(String rootDirectory) {
        FileSystemDataSource source = new FileSystemDataSource(rootDirectory);
        source.setRecursive(true);
        source.setFileExtensions(Arrays.asList(".dcm", ".dicom", ""));
        source.setMaxFiles(-1);
        return source;
    }
    
    /**
     * 创建快速扫描的文件系统数据源（限制文件数量）
     */
    public static FileSystemDataSource createQuickScan(String rootDirectory, int maxFiles) {
        FileSystemDataSource source = createDefault(rootDirectory);
        source.setMaxFiles(maxFiles);
        return source;
    }
    
    @Override
    public String toString() {
        return getConfigSummary();
    }
}
