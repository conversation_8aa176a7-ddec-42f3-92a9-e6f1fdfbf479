package com.ge.med.ct.dicom.core;

import com.ge.med.ct.dicom.model.*;
import com.ge.med.ct.exception.core.DicomException;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * DICOM数据存储管理器
 * 负责纯数据的存储和基本CRUD操作，职责单一
 */
public class DicomDataStorage {
    private static final Logger LOG = Logger.getLogger(DicomDataStorage.class.getName());

    // 数据存储
    private final Map<String, DicomExam> exams = new ConcurrentHashMap<>();
    private final Map<String, DicomSeries> series = new ConcurrentHashMap<>();
    private final Map<String, DicomImage> images = new ConcurrentHashMap<>();

    // 文件路径映射
    private final Map<String, String> imageToFilePath = new ConcurrentHashMap<>();

    // === 检查操作 ===

    /**
     * 添加检查
     */
    public void addExam(DicomExam exam) throws DicomException {
        if (exam == null) {
            throw new DicomException("检查对象不能为空");
        }
        
        exams.put(exam.getId(), exam);
        LOG.fine("添加检查: " + exam.getId());
    }

    /**
     * 获取检查
     */
    public DicomExam getExam(String examId) {
        return exams.get(examId);
    }

    /**
     * 获取所有检查
     */
    public Collection<DicomExam> getAllExams() {
        return exams.values();
    }

    /**
     * 移除检查
     */
    public DicomExam removeExam(String examId) {
        DicomExam removed = exams.remove(examId);
        if (removed != null) {
            LOG.fine("移除检查: " + examId);
        }
        return removed;
    }

    /**
     * 检查是否存在
     */
    public boolean containsExam(String examId) {
        return exams.containsKey(examId);
    }

    // === 序列操作 ===

    /**
     * 添加序列
     */
    public void addSeries(DicomSeries seriesItem) throws DicomException {
        if (seriesItem == null) {
            throw new DicomException("序列对象不能为空");
        }
        
        series.put(seriesItem.getId(), seriesItem);
        LOG.fine("添加序列: " + seriesItem.getId());
    }

    /**
     * 获取序列
     */
    public DicomSeries getSeries(String seriesId) {
        return series.get(seriesId);
    }

    /**
     * 获取所有序列
     */
    public Collection<DicomSeries> getAllSeries() {
        return series.values();
    }

    /**
     * 移除序列
     */
    public DicomSeries removeSeries(String seriesId) {
        DicomSeries removed = series.remove(seriesId);
        if (removed != null) {
            LOG.fine("移除序列: " + seriesId);
        }
        return removed;
    }

    /**
     * 序列是否存在
     */
    public boolean containsSeries(String seriesId) {
        return series.containsKey(seriesId);
    }

    // === 图像操作 ===

    /**
     * 添加图像
     */
    public void addImage(DicomImage image) throws DicomException {
        if (image == null) {
            throw new DicomException("图像对象不能为空");
        }
        
        images.put(image.getId(), image);
        
        // 同时更新文件路径映射
        if (image.getFilePath() != null) {
            imageToFilePath.put(image.getId(), image.getFilePath());
        }
        
        LOG.fine("添加图像: " + image.getId());
    }

    /**
     * 获取图像
     */
    public DicomImage getImage(String imageId) {
        return images.get(imageId);
    }

    /**
     * 获取所有图像
     */
    public Collection<DicomImage> getAllImages() {
        return images.values();
    }

    /**
     * 移除图像
     */
    public DicomImage removeImage(String imageId) {
        DicomImage removed = images.remove(imageId);
        imageToFilePath.remove(imageId);
        
        if (removed != null) {
            LOG.fine("移除图像: " + imageId);
        }
        return removed;
    }

    /**
     * 图像是否存在
     */
    public boolean containsImage(String imageId) {
        return images.containsKey(imageId);
    }

    // === 文件路径操作 ===

    /**
     * 设置图像文件路径
     */
    public void setImageFilePath(String imageId, String filePath) {
        if (imageId != null && filePath != null) {
            imageToFilePath.put(imageId, filePath);
            
            // 同时更新图像对象的路径
            DicomImage image = images.get(imageId);
            if (image != null) {
                image.setFilePath(filePath);
            }
        }
    }

    /**
     * 获取图像文件路径
     */
    public String getImageFilePath(String imageId) {
        return imageToFilePath.get(imageId);
    }

    /**
     * 批量获取图像文件路径
     */
    public Map<String, String> getImageFilePaths(List<String> imageIds) {
        Map<String, String> result = new HashMap<>();
        for (String imageId : imageIds) {
            String path = getImageFilePath(imageId);
            if (path != null) {
                result.put(imageId, path);
            }
        }
        return result;
    }

    /**
     * 根据文件路径查找图像
     */
    public DicomImage getImageByFilePath(String filePath) {
        for (Map.Entry<String, String> entry : imageToFilePath.entrySet()) {
            if (filePath.equals(entry.getValue())) {
                return images.get(entry.getKey());
            }
        }
        return null;
    }

    // === 统计信息 ===

    /**
     * 获取存储统计信息
     */
    public StorageStats getStats() {
        return new StorageStats(
            exams.size(),
            series.size(),
            images.size(),
            imageToFilePath.size()
        );
    }

    // === 清理操作 ===

    /**
     * 清除所有数据
     */
    public void clear() {
        exams.clear();
        series.clear();
        images.clear();
        imageToFilePath.clear();
        LOG.info("清除所有DICOM数据");
    }

    /**
     * 清除像素数据以释放内存
     */
    public void clearPixelData() {
        for (DicomImage image : images.values()) {
            image.clearPixelData();
        }
        LOG.info("清除所有像素数据");
    }

    /**
     * 存储统计信息
     */
    public static class StorageStats {
        private final int examCount;
        private final int seriesCount;
        private final int imageCount;
        private final int filePathCount;

        public StorageStats(int examCount, int seriesCount, int imageCount, int filePathCount) {
            this.examCount = examCount;
            this.seriesCount = seriesCount;
            this.imageCount = imageCount;
            this.filePathCount = filePathCount;
        }

        public int getExamCount() { return examCount; }
        public int getSeriesCount() { return seriesCount; }
        public int getImageCount() { return imageCount; }
        public int getFilePathCount() { return filePathCount; }

        @Override
        public String toString() {
            return String.format("StorageStats[exams=%d, series=%d, images=%d, filePaths=%d]", 
                examCount, seriesCount, imageCount, filePathCount);
        }
    }
}
