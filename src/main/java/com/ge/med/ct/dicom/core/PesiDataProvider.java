package com.ge.med.ct.dicom.core;

import com.ge.med.ct.dicom.model.*;
import com.ge.med.ct.exception.core.DicomException;

import java.util.*;
import java.util.logging.Logger;

/**
 * PESI数据提供者（重构版）
 * 通过PESI查询获取DICOM数据路径信息
 */
public class PesiDataProvider extends AbstractDicomDataProvider {
    private static final Logger LOG = Logger.getLogger(PesiDataProvider.class.getName());

    private PesiDataSource currentSource;
    private PesiQueryService pesiQueryService;

    @Override
    public DataSourceType getDataSourceType() {
        return DataSourceType.PESI;
    }

    @Override
    protected void doLoadData() throws Exception {
        if (currentSource == null) {
            throw new IllegalStateException("PESI数据源未设置");
        }

        LOG.info("开始从PESI加载数据: " + currentSource.getExecuteQueryScript());

        // 初始化PESI查询服务
        initializePesiService();

        // 执行PESI查询
        List<PesiQueryResult> results = executePesiQuery();
        LOG.info("PESI查询返回 " + results.size() + " 条记录");

        // 处理查询结果
        processPesiResults(results);

        LOG.info("PESI数据加载完成");
    }

    /**
     * 设置PESI数据源并加载数据
     */
    public void loadFromPesi(PesiDataSource source) throws Exception {
        this.currentSource = source;
        loadData();
    }

    // === 私有方法 ===

    /**
     * 初始化PESI查询服务
     */
    private void initializePesiService() {
        if (pesiQueryService == null) {
            pesiQueryService = new SimplePesiQueryService();
        }
    }

    /**
     * 执行PESI查询
     */
    private List<PesiQueryResult> executePesiQuery() throws Exception {
        // 构建查询命令
        String command = currentSource.buildFullCommand();
        LOG.info("执行PESI查询命令: " + command);

        // 执行查询（这里使用模拟实现）
        return pesiQueryService.executeQuery(command);
    }

    /**
     * 处理PESI查询结果
     */
    private void processPesiResults(List<PesiQueryResult> results) throws DicomException {
        Map<String, DicomExam> examMap = new HashMap<>();
        Map<String, DicomSeries> seriesMap = new HashMap<>();

        for (PesiQueryResult result : results) {
            try {
                // 创建或获取检查
                DicomExam exam = getOrCreateExam(result, examMap);
                
                // 创建或获取序列
                DicomSeries series = getOrCreateSeries(result, exam.getId(), seriesMap);
                
                // 创建图像
                DicomImage image = createImageFromPesiResult(result, series.getId());
                
                // 添加到存储
                if (!storage.containsExam(exam.getId())) {
                    addExam(exam);
                }
                if (!storage.containsSeries(series.getId())) {
                    addSeries(series);
                }
                addImage(image);
                
            } catch (Exception e) {
                LOG.warning("处理PESI结果失败: " + e.getMessage());
                // 继续处理其他结果
            }
        }
    }

    /**
     * 获取或创建检查
     */
    private DicomExam getOrCreateExam(PesiQueryResult result, Map<String, DicomExam> examMap) 
            throws DicomException {
        String examId = result.getStudyInstanceUID();
        
        DicomExam exam = examMap.get(examId);
        if (exam == null) {
            exam = new DicomExam(examId);
            exam.setPatientID(result.getPatientID());
            exam.setPatientName(result.getPatientName());
            exam.setStudyDate(result.getStudyDate());
            exam.setStudyDescription(result.getStudyDescription());
            exam.setStudyInstanceUID(result.getStudyInstanceUID());
            
            examMap.put(examId, exam);
        }
        
        return exam;
    }

    /**
     * 获取或创建序列
     */
    private DicomSeries getOrCreateSeries(PesiQueryResult result, String examId, 
                                         Map<String, DicomSeries> seriesMap) throws DicomException {
        String seriesId = result.getSeriesInstanceUID();
        
        DicomSeries series = seriesMap.get(seriesId);
        if (series == null) {
            series = new DicomSeries(seriesId, examId);
            series.setSeriesNumber(result.getSeriesNumber());
            series.setModality(result.getModality());
            series.setSeriesDescription(result.getSeriesDescription());
            series.setSeriesInstanceUID(result.getSeriesInstanceUID());
            
            seriesMap.put(seriesId, series);
        }
        
        return series;
    }

    /**
     * 从PESI结果创建图像
     */
    private DicomImage createImageFromPesiResult(PesiQueryResult result, String seriesId) 
            throws DicomException {
        String imageId = result.getSopInstanceUID();
        
        DicomImage image = new DicomImage(imageId, seriesId);
        image.setSopInstanceUID(result.getSopInstanceUID());
        image.setInstanceNumber(result.getInstanceNumber());
        
        // 设置PESI路径作为文件路径
        if (result.getPesiPath() != null) {
            image.setFilePath(result.getPesiPath());
            image.setFileName(extractFileNameFromPesiPath(result.getPesiPath()));
        }
        
        return image;
    }

    /**
     * 从PESI路径提取文件名
     */
    private String extractFileNameFromPesiPath(String pesiPath) {
        if (pesiPath == null || pesiPath.isEmpty()) {
            return "";
        }
        
        // PESI路径格式通常为: /path/to/file.dcm
        int lastSlash = pesiPath.lastIndexOf('/');
        if (lastSlash >= 0 && lastSlash < pesiPath.length() - 1) {
            return pesiPath.substring(lastSlash + 1);
        }
        
        return pesiPath;
    }

    /**
     * 获取当前数据源
     */
    public PesiDataSource getCurrentSource() {
        return currentSource;
    }

    /**
     * 设置数据源
     */
    public void setDataSource(PesiDataSource source) {
        this.currentSource = source;
    }

    // === 内部类：简单的PESI查询服务实现 ===

    /**
     * 简单的PESI查询服务实现
     */
    private static class SimplePesiQueryService implements PesiQueryService {
        
        @Override
        public List<PesiQueryResult> executeQuery(String command) throws Exception {
            // 这里应该执行实际的PESI查询
            // 为了演示，返回模拟数据
            return createMockPesiResults();
        }
        
        @Override
        public boolean isServiceAvailable() {
            // 检查PESI服务是否可用
            return true; // 模拟返回true
        }
        
        /**
         * 创建模拟的PESI查询结果
         */
        private List<PesiQueryResult> createMockPesiResults() {
            List<PesiQueryResult> results = new ArrayList<>();
            
            // 创建一些模拟数据
            for (int i = 1; i <= 10; i++) {
                PesiQueryResult result = new PesiQueryResult();
                result.setPatientID("P" + String.format("%04d", i));
                result.setPatientName("Patient_" + i);
                result.setStudyInstanceUID("*******.5." + i);
                result.setStudyDate("2024010" + (i % 9 + 1));
                result.setStudyDescription("CT Study " + i);
                result.setSeriesInstanceUID("*******.5." + i + ".1");
                result.setSeriesNumber(String.valueOf(i));
                result.setModality("CT");
                result.setSeriesDescription("Axial CT Series " + i);
                result.setSopInstanceUID("*******.5." + i + ".1." + i);
                result.setInstanceNumber(String.valueOf(i));
                result.setPesiPath("/pesi/data/patient" + i + "/series" + i + "/image" + i + ".dcm");
                
                results.add(result);
            }
            
            return results;
        }
    }
}

/**
 * PESI查询服务接口
 */
interface PesiQueryService {
    List<PesiQueryResult> executeQuery(String command) throws Exception;
    boolean isServiceAvailable();
}

/**
 * PESI查询结果
 */
class PesiQueryResult {
    private String patientID;
    private String patientName;
    private String studyInstanceUID;
    private String studyDate;
    private String studyDescription;
    private String seriesInstanceUID;
    private String seriesNumber;
    private String modality;
    private String seriesDescription;
    private String sopInstanceUID;
    private String instanceNumber;
    private String pesiPath;
    
    // Getter和Setter方法
    public String getPatientID() { return patientID; }
    public void setPatientID(String patientID) { this.patientID = patientID; }
    
    public String getPatientName() { return patientName; }
    public void setPatientName(String patientName) { this.patientName = patientName; }
    
    public String getStudyInstanceUID() { return studyInstanceUID; }
    public void setStudyInstanceUID(String studyInstanceUID) { this.studyInstanceUID = studyInstanceUID; }
    
    public String getStudyDate() { return studyDate; }
    public void setStudyDate(String studyDate) { this.studyDate = studyDate; }
    
    public String getStudyDescription() { return studyDescription; }
    public void setStudyDescription(String studyDescription) { this.studyDescription = studyDescription; }
    
    public String getSeriesInstanceUID() { return seriesInstanceUID; }
    public void setSeriesInstanceUID(String seriesInstanceUID) { this.seriesInstanceUID = seriesInstanceUID; }
    
    public String getSeriesNumber() { return seriesNumber; }
    public void setSeriesNumber(String seriesNumber) { this.seriesNumber = seriesNumber; }
    
    public String getModality() { return modality; }
    public void setModality(String modality) { this.modality = modality; }
    
    public String getSeriesDescription() { return seriesDescription; }
    public void setSeriesDescription(String seriesDescription) { this.seriesDescription = seriesDescription; }
    
    public String getSopInstanceUID() { return sopInstanceUID; }
    public void setSopInstanceUID(String sopInstanceUID) { this.sopInstanceUID = sopInstanceUID; }
    
    public String getInstanceNumber() { return instanceNumber; }
    public void setInstanceNumber(String instanceNumber) { this.instanceNumber = instanceNumber; }
    
    public String getPesiPath() { return pesiPath; }
    public void setPesiPath(String pesiPath) { this.pesiPath = pesiPath; }
}
