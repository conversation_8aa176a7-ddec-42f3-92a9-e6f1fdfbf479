package com.ge.med.ct.dicom;

import com.ge.med.ct.dicom.core.*;
import com.ge.med.ct.dicom.model.*;
import com.ge.med.ct.dicom.storage.DicomExporter;
import com.ge.med.ct.dicom.table.SimpleTableConverter;

import java.util.List;
import java.util.Map;

/**
 * DICOM模块使用示例
 * 演示重构后的DICOM模块的主要功能
 */
public class DicomModuleExample {

    public static void main(String[] args) {
        DicomModuleExample example = new DicomModuleExample();
        
        try {
            // 演示文件系统数据加载
            example.demonstrateFileSystemLoading();
            
            // 演示PESI数据加载
            example.demonstratePesiLoading();
            
            // 演示数据查询
            example.demonstrateDataQuery();
            
            // 演示表格转换
            example.demonstrateTableConversion();
            
            // 演示数据导出
            example.demonstrateDataExport();
            
        } catch (Exception e) {
            System.err.println("示例执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 演示文件系统数据加载
     */
    public void demonstrateFileSystemLoading() throws Exception {
        System.out.println("=== 文件系统数据加载演示 ===");
        
        // 获取数据服务
        DicomDataService service = DicomDataServiceImpl.getInstance();
        
        // 创建文件系统数据源
        FileSystemDataSource source = FileSystemDataSource.createDefault("C:/DICOM_DATA");
        source.setMaxFiles(100); // 限制文件数量用于演示
        
        // 创建进度回调
        DicomDataService.ProgressCallback callback = new DicomDataService.ProgressCallback() {
            @Override
            public void onProgress(DicomDataService.ProgressInfo info) {
                System.out.println("  进度: " + info.getMessage() + " - " + info.getPercentage() + "%");
            }
            
            @Override
            public void onError(String error) {
                System.err.println("  错误: " + error);
            }
            
            @Override
            public void onComplete() {
                System.out.println("  文件系统数据加载完成!");
            }
        };
        
        // 加载数据
        service.loadDicomData(source, callback);
        
        // 显示统计信息
        DicomQueryService.DataStats stats = service.getDataStats();
        System.out.println("  统计信息: " + stats);
        System.out.println();
    }

    /**
     * 演示PESI数据加载
     */
    public void demonstratePesiLoading() throws Exception {
        System.out.println("=== PESI数据加载演示 ===");
        
        // 获取数据服务
        DicomDataService service = DicomDataServiceImpl.getInstance();
        
        // 创建PESI数据源
        PesiDataSource pesiSource = PesiDataSource.createDefault();
        pesiSource.addParameter("patientName", "Test Patient");
        
        // 创建进度回调
        DicomDataService.ProgressCallback callback = new DicomDataService.ProgressCallback() {
            @Override
            public void onProgress(DicomDataService.ProgressInfo info) {
                System.out.println("  进度: " + info.getMessage() + " - " + info.getPercentage() + "%");
            }
            
            @Override
            public void onError(String error) {
                System.err.println("  错误: " + error);
            }
            
            @Override
            public void onComplete() {
                System.out.println("  PESI数据加载完成!");
            }
        };
        
        // 加载数据
        service.loadDicomData(pesiSource, callback);
        
        // 显示统计信息
        DicomQueryService.DataStats stats = service.getDataStats();
        System.out.println("  统计信息: " + stats);
        System.out.println();
    }

    /**
     * 演示数据查询
     */
    public void demonstrateDataQuery() throws Exception {
        System.out.println("=== 数据查询演示 ===");
        
        // 获取数据提供者
        DicomDataService service = DicomDataServiceImpl.getInstance();
        DicomDataProvider provider = service.getDataProvider();
        
        // 基本查询
        List<DicomExam> allExams = provider.getAllExams();
        System.out.println("  总检查数: " + allExams.size());
        
        if (!allExams.isEmpty()) {
            DicomExam firstExam = allExams.get(0);
            System.out.println("  第一个检查: " + firstExam.getDisplayName());
            
            // 查询检查下的序列
            List<DicomSeries> seriesForExam = provider.getSeriesForExam(firstExam.getId());
            System.out.println("  该检查的序列数: " + seriesForExam.size());
            
            if (!seriesForExam.isEmpty()) {
                DicomSeries firstSeries = seriesForExam.get(0);
                System.out.println("  第一个序列: " + firstSeries.getDisplayName());
                
                // 查询序列下的图像
                List<DicomImage> imagesForSeries = provider.getImagesForSeries(firstSeries.getId());
                System.out.println("  该序列的图像数: " + imagesForSeries.size());
                
                if (!imagesForSeries.isEmpty()) {
                    DicomImage firstImage = imagesForSeries.get(0);
                    System.out.println("  第一个图像: " + firstImage.getDisplayName());
                    
                    // 查询图像文件路径
                    String filePath = provider.getImageFilePath(firstImage.getId());
                    System.out.println("  图像文件路径: " + filePath);
                    
                    // 查询图像元数据
                    Map<String, String> metadata = provider.getImageMetadata(firstImage.getId());
                    System.out.println("  图像元数据标签数: " + metadata.size());
                }
            }
        }
        System.out.println();
    }

    /**
     * 演示表格转换
     */
    public void demonstrateTableConversion() throws Exception {
        System.out.println("=== 表格转换演示 ===");
        
        // 获取数据提供者
        DicomDataService service = DicomDataServiceImpl.getInstance();
        DicomDataProvider provider = service.getDataProvider();
        
        // 转换检查数据为表格
        List<DicomExam> exams = provider.getAllExams();
        SimpleTableConverter.TableData examTable = SimpleTableConverter.createExamTableData(exams);
        
        System.out.println("  检查表格:");
        System.out.println("    列数: " + examTable.getColumnCount());
        System.out.println("    行数: " + examTable.getRowCount());
        System.out.println("    列标题: " + examTable.getHeaders());
        
        // 显示前几行数据
        List<List<String>> rows = examTable.getRows();
        int displayRows = Math.min(3, rows.size());
        for (int i = 0; i < displayRows; i++) {
            System.out.println("    数据行" + (i+1) + ": " + rows.get(i));
        }
        
        // 转换序列数据
        List<DicomSeries> seriesList = provider.getAllSeries();
        SimpleTableConverter.TableData seriesTable = SimpleTableConverter.createSeriesTableData(seriesList);
        System.out.println("  序列表格: " + seriesTable.getColumnCount() + "列, " + seriesTable.getRowCount() + "行");
        
        // 转换图像数据
        List<DicomImage> images = provider.getAllImages();
        SimpleTableConverter.TableData imageTable = SimpleTableConverter.createImageTableData(images);
        System.out.println("  图像表格: " + imageTable.getColumnCount() + "列, " + imageTable.getRowCount() + "行");
        
        System.out.println();
    }

    /**
     * 演示数据导出
     */
    public void demonstrateDataExport() throws Exception {
        System.out.println("=== 数据导出演示 ===");
        
        // 获取数据提供者
        DicomDataService service = DicomDataServiceImpl.getInstance();
        DicomDataProvider provider = service.getDataProvider();
        
        // 创建导出器
        DicomExporter exporter = new DicomExporter();
        
        try {
            // 导出检查数据为CSV
            String csvFile = exporter.exportToCsv(provider, "dicom_exams.csv", DicomExporter.ExportLevel.EXAM);
            System.out.println("  检查数据导出为CSV: " + csvFile);
            
            // 导出序列数据为JSON
            String jsonFile = exporter.exportToJson(provider, "dicom_series.json", DicomExporter.ExportLevel.SERIES);
            System.out.println("  序列数据导出为JSON: " + jsonFile);
            
            // 导出统计信息
            String statsFile = exporter.exportStatistics(provider, "dicom_stats.json");
            System.out.println("  统计信息导出: " + statsFile);
            
        } catch (Exception e) {
            System.err.println("  导出失败: " + e.getMessage());
        }
        
        System.out.println();
    }

    /**
     * 演示核心组件直接使用
     */
    public void demonstrateCoreComponents() throws Exception {
        System.out.println("=== 核心组件直接使用演示 ===");
        
        // 创建核心组件
        DicomDataStorage storage = new DicomDataStorage();
        DicomRelationshipManager relationManager = new DicomRelationshipManager();
        DicomQueryService queryService = new DicomQueryService(storage, relationManager);
        
        // 创建测试数据
        DicomExam exam = new DicomExam("test_exam_1");
        exam.setPatientID("P001");
        exam.setPatientName("Test Patient");
        exam.setStudyDate("20240101");
        
        DicomSeries series = new DicomSeries("test_series_1", "test_exam_1");
        series.setSeriesNumber("1");
        series.setModality("CT");
        
        DicomImage image = new DicomImage("test_image_1", "test_series_1");
        image.setInstanceNumber("1");
        image.setRows(512);
        image.setColumns(512);
        
        // 添加数据
        storage.addExam(exam);
        storage.addSeries(series);
        storage.addImage(image);
        
        // 建立关系
        relationManager.addExamSeriesRelation("test_exam_1", "test_series_1");
        relationManager.addSeriesImageRelation("test_series_1", "test_image_1");
        
        // 查询数据
        List<DicomSeries> seriesForExam = queryService.getSeriesForExam("test_exam_1");
        List<DicomImage> imagesForSeries = queryService.getImagesForSeries("test_series_1");
        
        System.out.println("  创建的检查: " + exam.getDisplayName());
        System.out.println("  检查下的序列数: " + seriesForExam.size());
        System.out.println("  序列下的图像数: " + imagesForSeries.size());
        
        // 验证数据完整性
        boolean isValid = queryService.validateDataIntegrity();
        System.out.println("  数据完整性验证: " + (isValid ? "通过" : "失败"));
        
        // 获取统计信息
        DicomQueryService.DataStats stats = queryService.getDataStats();
        System.out.println("  统计信息: " + stats);
        
        System.out.println();
    }
}
