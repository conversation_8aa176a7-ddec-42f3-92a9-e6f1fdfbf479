package com.ge.med.ct.dicom.core;

import com.ge.med.ct.dicom.model.*;
import com.ge.med.ct.exception.core.DicomException;

import java.util.*;
import java.util.logging.Logger;

/**
 * 抽象DICOM数据提供者（重构版）
 * 职责分离，使用组合模式管理数据存储、关系和查询
 */
public abstract class AbstractDicomDataProvider implements DicomDataProvider {
    private static final Logger LOG = Logger.getLogger(AbstractDicomDataProvider.class.getName());

    // 组合的核心组件
    protected final DicomDataStorage storage;
    protected final DicomRelationshipManager relationshipManager;
    protected final DicomQueryService queryService;

    // 状态管理
    private boolean dataLoaded = false;
    private long lastLoadTime = 0;

    protected AbstractDicomDataProvider() {
        this.storage = new DicomDataStorage();
        this.relationshipManager = new DicomRelationshipManager();
        this.queryService = new DicomQueryService(storage, relationshipManager);
        
        LOG.info("创建DICOM数据提供者: " + getClass().getSimpleName());
    }

    // === 抽象方法，由子类实现 ===

    /**
     * 执行实际的数据加载
     */
    protected abstract void doLoadData() throws Exception;

    /**
     * 获取数据源类型
     */
    public abstract DataSourceType getDataSourceType();

    // === DicomDataProvider接口实现 ===

    @Override
    public void loadData() throws Exception {
        LOG.info("开始加载DICOM数据...");
        long startTime = System.currentTimeMillis();
        
        try {
            // 清除现有数据
            clearData();
            
            // 执行具体的加载逻辑
            doLoadData();
            
            // 验证数据完整性
            if (!queryService.validateDataIntegrity()) {
                throw new DicomException("数据完整性验证失败");
            }
            
            dataLoaded = true;
            lastLoadTime = System.currentTimeMillis();
            
            long duration = lastLoadTime - startTime;
            DicomQueryService.DataStats stats = queryService.getDataStats();
            LOG.info(String.format("DICOM数据加载完成，耗时: %d ms, 统计: %s", duration, stats));
            
        } catch (Exception e) {
            LOG.severe("DICOM数据加载失败: " + e.getMessage());
            clearData();
            throw e;
        }
    }

    @Override
    public void refreshData() throws Exception {
        LOG.info("刷新DICOM数据...");
        loadData();
    }

    @Override
    public void clearData() {
        storage.clear();
        relationshipManager.clear();
        dataLoaded = false;
        lastLoadTime = 0;
        LOG.info("清除DICOM数据");
    }

    // === DicomQueryProvider接口实现 ===

    @Override
    public List<DicomExam> getAllExams() {
        ensureDataLoaded();
        return queryService.getAllExams();
    }

    @Override
    public DicomExam getExam(String examId) {
        ensureDataLoaded();
        return queryService.getExam(examId);
    }

    @Override
    public List<DicomSeries> getSeriesForExam(String examId) {
        ensureDataLoaded();
        return queryService.getSeriesForExam(examId);
    }

    @Override
    public DicomSeries getSeries(String seriesId) {
        ensureDataLoaded();
        return queryService.getSeries(seriesId);
    }

    @Override
    public List<DicomImage> getImagesForSeries(String seriesId) {
        ensureDataLoaded();
        return queryService.getImagesForSeries(seriesId);
    }

    @Override
    public DicomImage getImage(String imageId) {
        ensureDataLoaded();
        return queryService.getImage(imageId);
    }

    // === DicomFilePathProvider接口实现 ===

    @Override
    public String getImageFilePath(String imageId) {
        ensureDataLoaded();
        return queryService.getImageFilePath(imageId);
    }

    @Override
    public Map<String, String> getImageFilePaths(List<String> imageIds) {
        ensureDataLoaded();
        return queryService.getImageFilePaths(imageIds);
    }

    @Override
    public DicomImage getImageByFilePath(String filePath) {
        ensureDataLoaded();
        return queryService.getImageByFilePath(filePath);
    }

    // === DicomMetadataProvider接口实现 ===

    @Override
    public String getTagValue(String imageId, String tagId) {
        ensureDataLoaded();
        DicomImage image = queryService.getImage(imageId);
        return image != null ? image.getTagValue(tagId) : null;
    }

    @Override
    public Map<String, String> getImageMetadata(String imageId) {
        ensureDataLoaded();
        DicomImage image = queryService.getImage(imageId);
        if (image == null) {
            return Collections.emptyMap();
        }

        Map<String, String> metadata = new HashMap<>();
        for (Map.Entry<String, DicomTag> entry : image.getTags().entrySet()) {
            metadata.put(entry.getKey(), entry.getValue().getValueAsString());
        }
        return metadata;
    }

    // === DicomStatsProvider接口实现 ===

    @Override
    public DicomQueryService.DataStats getDataStats() {
        ensureDataLoaded();
        return queryService.getDataStats();
    }

    @Override
    public int getExamCount() {
        ensureDataLoaded();
        return queryService.getExamCount();
    }

    @Override
    public int getSeriesCount() {
        ensureDataLoaded();
        return queryService.getSeriesCount();
    }

    @Override
    public int getImageCount() {
        ensureDataLoaded();
        return queryService.getImageCount();
    }

    // === 工具方法 ===

    /**
     * 添加检查
     */
    protected void addExam(DicomExam exam) throws DicomException {
        storage.addExam(exam);
        LOG.fine("添加检查: " + exam.getId());
    }

    /**
     * 添加序列
     */
    protected void addSeries(DicomSeries series) throws DicomException {
        storage.addSeries(series);
        relationshipManager.addExamSeriesRelation(series.getExamId(), series.getId());
        LOG.fine("添加序列: " + series.getId() + " (检查: " + series.getExamId() + ")");
    }

    /**
     * 添加图像
     */
    protected void addImage(DicomImage image) throws DicomException {
        storage.addImage(image);
        relationshipManager.addSeriesImageRelation(image.getSeriesId(), image.getId());
        LOG.fine("添加图像: " + image.getId() + " (序列: " + image.getSeriesId() + ")");
    }

    /**
     * 设置图像文件路径
     */
    protected void setImageFilePath(String imageId, String filePath) {
        storage.setImageFilePath(imageId, filePath);
    }

    /**
     * 确保数据已加载
     */
    private void ensureDataLoaded() {
        if (!dataLoaded) {
            throw new IllegalStateException("数据尚未加载，请先调用loadData()方法");
        }
    }

    /**
     * 检查数据是否已加载
     */
    public boolean isDataLoaded() {
        return dataLoaded;
    }

    /**
     * 获取最后加载时间
     */
    public long getLastLoadTime() {
        return lastLoadTime;
    }

    /**
     * 获取数据加载耗时
     */
    public long getLoadDuration() {
        return dataLoaded ? (lastLoadTime - (lastLoadTime - getLoadDuration())) : 0;
    }

    @Override
    public String toString() {
        return String.format("%s[type=%s, loaded=%s, stats=%s]",
                getClass().getSimpleName(),
                getDataSourceType(),
                dataLoaded,
                dataLoaded ? getDataStats() : "未加载");
    }
}
