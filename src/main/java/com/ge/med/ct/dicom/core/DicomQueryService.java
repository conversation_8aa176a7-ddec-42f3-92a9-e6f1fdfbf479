package com.ge.med.ct.dicom.core;

import com.ge.med.ct.dicom.model.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.logging.Logger;

/**
 * DICOM查询服务
 * 提供统一的查询接口，结合数据存储和关系管理
 */
public class DicomQueryService {
    private static final Logger LOG = Logger.getLogger(DicomQueryService.class.getName());

    private final DicomDataStorage storage;
    private final DicomRelationshipManager relationshipManager;

    public DicomQueryService(DicomDataStorage storage, DicomRelationshipManager relationshipManager) {
        this.storage = storage;
        this.relationshipManager = relationshipManager;
    }

    // === 基本查询方法 ===

    /**
     * 获取所有检查
     */
    public List<DicomExam> getAllExams() {
        return new ArrayList<>(storage.getAllExams());
    }

    /**
     * 获取检查
     */
    public DicomExam getExam(String examId) {
        return storage.getExam(examId);
    }

    /**
     * 获取所有序列
     */
    public List<DicomSeries> getAllSeries() {
        return new ArrayList<>(storage.getAllSeries());
    }

    /**
     * 获取序列
     */
    public DicomSeries getSeries(String seriesId) {
        return storage.getSeries(seriesId);
    }

    /**
     * 获取所有图像
     */
    public List<DicomImage> getAllImages() {
        return new ArrayList<>(storage.getAllImages());
    }

    /**
     * 获取图像
     */
    public DicomImage getImage(String imageId) {
        return storage.getImage(imageId);
    }

    // === 关系查询方法 ===

    /**
     * 获取检查下的所有序列
     */
    public List<DicomSeries> getSeriesForExam(String examId) {
        List<String> seriesIds = relationshipManager.getSeriesForExam(examId);
        return seriesIds.stream()
                .map(storage::getSeries)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取序列下的所有图像
     */
    public List<DicomImage> getImagesForSeries(String seriesId) {
        List<String> imageIds = relationshipManager.getImagesForSeries(seriesId);
        return imageIds.stream()
                .map(storage::getImage)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取检查下的所有图像
     */
    public List<DicomImage> getImagesForExam(String examId) {
        List<DicomImage> allImages = new ArrayList<>();
        List<DicomSeries> seriesList = getSeriesForExam(examId);
        
        for (DicomSeries series : seriesList) {
            allImages.addAll(getImagesForSeries(series.getId()));
        }
        
        return allImages;
    }

    /**
     * 获取图像所属的序列
     */
    public DicomSeries getSeriesForImage(String imageId) {
        String seriesId = relationshipManager.getSeriesForImage(imageId);
        return seriesId != null ? storage.getSeries(seriesId) : null;
    }

    /**
     * 获取图像所属的检查
     */
    public DicomExam getExamForImage(String imageId) {
        String examId = relationshipManager.getExamForImage(imageId);
        return examId != null ? storage.getExam(examId) : null;
    }

    /**
     * 获取序列所属的检查
     */
    public DicomExam getExamForSeries(String seriesId) {
        String examId = relationshipManager.getExamForSeries(seriesId);
        return examId != null ? storage.getExam(examId) : null;
    }

    // === 文件路径查询 ===

    /**
     * 获取图像文件路径
     */
    public String getImageFilePath(String imageId) {
        return storage.getImageFilePath(imageId);
    }

    /**
     * 批量获取图像文件路径
     */
    public Map<String, String> getImageFilePaths(List<String> imageIds) {
        return storage.getImageFilePaths(imageIds);
    }

    /**
     * 根据文件路径查找图像
     */
    public DicomImage getImageByFilePath(String filePath) {
        return storage.getImageByFilePath(filePath);
    }

    // === 条件查询方法 ===

    /**
     * 根据患者ID查找检查
     */
    public List<DicomExam> getExamsByPatientId(String patientId) {
        if (patientId == null || patientId.trim().isEmpty()) {
            return Collections.emptyList();
        }

        return storage.getAllExams().stream()
                .filter(exam -> patientId.equals(exam.getPatientID()))
                .collect(Collectors.toList());
    }

    /**
     * 根据患者姓名查找检查
     */
    public List<DicomExam> getExamsByPatientName(String patientName) {
        if (patientName == null || patientName.trim().isEmpty()) {
            return Collections.emptyList();
        }

        String searchName = patientName.toLowerCase().trim();
        return storage.getAllExams().stream()
                .filter(exam -> {
                    String name = exam.getPatientName();
                    return name != null && name.toLowerCase().contains(searchName);
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据检查日期查找检查
     */
    public List<DicomExam> getExamsByStudyDate(String studyDate) {
        if (studyDate == null || studyDate.trim().isEmpty()) {
            return Collections.emptyList();
        }

        return storage.getAllExams().stream()
                .filter(exam -> studyDate.equals(exam.getStudyDate()))
                .collect(Collectors.toList());
    }

    /**
     * 根据模态查找序列
     */
    public List<DicomSeries> getSeriesByModality(String modality) {
        if (modality == null || modality.trim().isEmpty()) {
            return Collections.emptyList();
        }

        return storage.getAllSeries().stream()
                .filter(series -> modality.equalsIgnoreCase(series.getModality()))
                .collect(Collectors.toList());
    }

    /**
     * 根据序列号查找序列
     */
    public List<DicomSeries> getSeriesBySeriesNumber(String seriesNumber) {
        if (seriesNumber == null || seriesNumber.trim().isEmpty()) {
            return Collections.emptyList();
        }

        return storage.getAllSeries().stream()
                .filter(series -> seriesNumber.equals(series.getSeriesNumber()))
                .collect(Collectors.toList());
    }

    // === 统计查询方法 ===

    /**
     * 获取检查数量
     */
    public int getExamCount() {
        return storage.getAllExams().size();
    }

    /**
     * 获取序列数量
     */
    public int getSeriesCount() {
        return storage.getAllSeries().size();
    }

    /**
     * 获取图像数量
     */
    public int getImageCount() {
        return storage.getAllImages().size();
    }

    /**
     * 获取检查下的序列数量
     */
    public int getSeriesCountForExam(String examId) {
        return relationshipManager.getSeriesForExam(examId).size();
    }

    /**
     * 获取序列下的图像数量
     */
    public int getImageCountForSeries(String seriesId) {
        return relationshipManager.getImagesForSeries(seriesId).size();
    }

    /**
     * 获取检查下的图像数量
     */
    public int getImageCountForExam(String examId) {
        List<DicomSeries> seriesList = getSeriesForExam(examId);
        return seriesList.stream()
                .mapToInt(series -> getImageCountForSeries(series.getId()))
                .sum();
    }

    // === 验证方法 ===

    /**
     * 验证数据完整性
     */
    public boolean validateDataIntegrity() {
        // 验证关系一致性
        if (!relationshipManager.validateConsistency()) {
            LOG.warning("关系管理器数据不一致");
            return false;
        }

        // 验证序列引用的检查存在
        for (DicomSeries series : storage.getAllSeries()) {
            if (!storage.containsExam(series.getExamId())) {
                LOG.warning("序列 " + series.getId() + " 引用的检查 " + series.getExamId() + " 不存在");
                return false;
            }
        }

        // 验证图像引用的序列存在
        for (DicomImage image : storage.getAllImages()) {
            if (!storage.containsSeries(image.getSeriesId())) {
                LOG.warning("图像 " + image.getId() + " 引用的序列 " + image.getSeriesId() + " 不存在");
                return false;
            }
        }

        return true;
    }

    /**
     * 获取数据统计信息
     */
    public DataStats getDataStats() {
        DicomDataStorage.StorageStats storageStats = storage.getStats();
        DicomRelationshipManager.RelationshipStats relationStats = relationshipManager.getStats();
        
        return new DataStats(
            storageStats.getExamCount(),
            storageStats.getSeriesCount(),
            storageStats.getImageCount(),
            storageStats.getFilePathCount(),
            relationStats.getExamCount(),
            relationStats.getSeriesCount(),
            relationStats.getImageCount()
        );
    }

    /**
     * 数据统计信息
     */
    public static class DataStats {
        private final int examCount;
        private final int seriesCount;
        private final int imageCount;
        private final int filePathCount;
        private final int examRelationCount;
        private final int seriesRelationCount;
        private final int imageRelationCount;

        public DataStats(int examCount, int seriesCount, int imageCount, int filePathCount,
                        int examRelationCount, int seriesRelationCount, int imageRelationCount) {
            this.examCount = examCount;
            this.seriesCount = seriesCount;
            this.imageCount = imageCount;
            this.filePathCount = filePathCount;
            this.examRelationCount = examRelationCount;
            this.seriesRelationCount = seriesRelationCount;
            this.imageRelationCount = imageRelationCount;
        }

        public int getExamCount() { return examCount; }
        public int getSeriesCount() { return seriesCount; }
        public int getImageCount() { return imageCount; }
        public int getFilePathCount() { return filePathCount; }
        public int getExamRelationCount() { return examRelationCount; }
        public int getSeriesRelationCount() { return seriesRelationCount; }
        public int getImageRelationCount() { return imageRelationCount; }

        @Override
        public String toString() {
            return String.format("DataStats[exams=%d, series=%d, images=%d, filePaths=%d, " +
                    "examRels=%d, seriesRels=%d, imageRels=%d]",
                    examCount, seriesCount, imageCount, filePathCount,
                    examRelationCount, seriesRelationCount, imageRelationCount);
        }
    }
}
