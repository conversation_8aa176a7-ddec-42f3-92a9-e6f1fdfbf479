package com.ge.med.ct.dicom.core;

import java.util.HashMap;
import java.util.Map;

/**
 * PESI数据源配置
 * 配置从PESI数据库加载DICOM数据的参数
 */
public class PesiDataSource implements DicomDataSource {
    
    private String executeQueryScript;
    private String queryCommand;
    private Map<String, String> parameters;
    private int timeoutSeconds = 300; // 5分钟超时
    private boolean useCache = true;
    
    public PesiDataSource(String executeQueryScript) {
        this.executeQueryScript = executeQueryScript;
        this.parameters = new HashMap<>();
    }
    
    // === DicomDataSource接口实现 ===
    
    @Override
    public DicomDataProvider.DataSourceType getType() {
        return DicomDataProvider.DataSourceType.PESI;
    }
    
    @Override
    public String getDescription() {
        return "PESI数据源: " + executeQueryScript;
    }
    
    @Override
    public boolean isValid() {
        return executeQueryScript != null && !executeQueryScript.trim().isEmpty();
    }
    
    @Override
    public String getConfigSummary() {
        return String.format("PESI[script=%s, command=%s, timeout=%ds, cache=%s]",
                executeQueryScript, queryCommand, timeoutSeconds, useCache);
    }
    
    // === Getter和Setter方法 ===
    
    public String getExecuteQueryScript() {
        return executeQueryScript;
    }
    
    public void setExecuteQueryScript(String executeQueryScript) {
        this.executeQueryScript = executeQueryScript;
    }
    
    public String getQueryCommand() {
        return queryCommand;
    }
    
    public void setQueryCommand(String queryCommand) {
        this.queryCommand = queryCommand;
    }
    
    public Map<String, String> getParameters() {
        return parameters;
    }
    
    public void setParameters(Map<String, String> parameters) {
        this.parameters = parameters != null ? parameters : new HashMap<>();
    }
    
    public void addParameter(String key, String value) {
        this.parameters.put(key, value);
    }
    
    public String getParameter(String key) {
        return this.parameters.get(key);
    }
    
    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    public void setTimeoutSeconds(int timeoutSeconds) {
        this.timeoutSeconds = Math.max(30, timeoutSeconds); // 最少30秒
    }
    
    public boolean isUseCache() {
        return useCache;
    }
    
    public void setUseCache(boolean useCache) {
        this.useCache = useCache;
    }
    
    // === 工具方法 ===
    
    /**
     * 创建默认的PESI数据源
     */
    public static PesiDataSource createDefault() {
        PesiDataSource source = new PesiDataSource("executeQuery.bat");
        source.setTimeoutSeconds(300);
        source.setUseCache(true);
        return source;
    }
    
    /**
     * 创建带查询命令的PESI数据源
     */
    public static PesiDataSource createWithCommand(String executeQueryScript, String queryCommand) {
        PesiDataSource source = new PesiDataSource(executeQueryScript);
        source.setQueryCommand(queryCommand);
        return source;
    }
    
    /**
     * 创建用于患者搜索的PESI数据源
     */
    public static PesiDataSource createForPatientSearch(String patientName) {
        PesiDataSource source = createDefault();
        source.addParameter("patientName", patientName);
        source.setQueryCommand("searchPatient");
        return source;
    }
    
    /**
     * 创建用于序列搜索的PESI数据源
     */
    public static PesiDataSource createForSeriesSearch(String seriesDescription) {
        PesiDataSource source = createDefault();
        source.addParameter("seriesDescription", seriesDescription);
        source.setQueryCommand("searchSeries");
        return source;
    }
    
    /**
     * 构建完整的查询命令
     */
    public String buildFullCommand() {
        StringBuilder command = new StringBuilder();
        command.append(executeQueryScript);
        
        if (queryCommand != null && !queryCommand.isEmpty()) {
            command.append(" ").append(queryCommand);
        }
        
        for (Map.Entry<String, String> param : parameters.entrySet()) {
            command.append(" -").append(param.getKey()).append(" \"").append(param.getValue()).append("\"");
        }
        
        return command.toString();
    }
    
    /**
     * 获取参数数量
     */
    public int getParameterCount() {
        return parameters.size();
    }
    
    /**
     * 检查是否有特定参数
     */
    public boolean hasParameter(String key) {
        return parameters.containsKey(key);
    }
    
    /**
     * 清除所有参数
     */
    public void clearParameters() {
        parameters.clear();
    }
    
    @Override
    public String toString() {
        return getConfigSummary();
    }
}
