package com.ge.med.ct.dicom.storage;

import com.ge.med.ct.dicom.core.DicomDataProvider;
import com.ge.med.ct.dicom.core.DicomQueryService;
import com.ge.med.ct.dicom.model.*;
import com.ge.med.ct.dicom.table.SimpleTableConverter;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.logging.Logger;

/**
 * DICOM数据导出器（重构版）
 * 支持多种格式的数据导出
 */
public class DicomExporter {
    private static final Logger LOG = Logger.getLogger(DicomExporter.class.getName());

    /**
     * 导出级别枚举
     */
    public enum ExportLevel {
        EXAM("检查级别"),
        SERIES("序列级别"),
        IMAGE("图像级别");

        private final String description;

        ExportLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 导出格式枚举
     */
    public enum ExportFormat {
        CSV("CSV格式"),
        JSON("JSON格式"),
        XML("XML格式"),
        TXT("文本格式");

        private final String description;

        ExportFormat(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // === 主要导出方法 ===

    /**
     * 导出为CSV格式
     */
    public String exportToCsv(DicomDataProvider provider, String filePath, ExportLevel level) 
            throws IOException {
        LOG.info("开始导出CSV: " + filePath + ", 级别: " + level);

        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath, StandardCharsets.UTF_8))) {
            switch (level) {
                case EXAM:
                    exportExamsToCsv(provider, writer);
                    break;
                case SERIES:
                    exportSeriesToCsv(provider, writer);
                    break;
                case IMAGE:
                    exportImagesToCsv(provider, writer);
                    break;
            }
        }

        LOG.info("CSV导出完成: " + filePath);
        return filePath;
    }

    /**
     * 导出为JSON格式
     */
    public String exportToJson(DicomDataProvider provider, String filePath, ExportLevel level) 
            throws IOException {
        LOG.info("开始导出JSON: " + filePath + ", 级别: " + level);

        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath, StandardCharsets.UTF_8))) {
            switch (level) {
                case EXAM:
                    exportExamsToJson(provider, writer);
                    break;
                case SERIES:
                    exportSeriesToJson(provider, writer);
                    break;
                case IMAGE:
                    exportImagesToJson(provider, writer);
                    break;
            }
        }

        LOG.info("JSON导出完成: " + filePath);
        return filePath;
    }

    /**
     * 导出统计信息
     */
    public String exportStatistics(DicomDataProvider provider, String filePath) throws IOException {
        LOG.info("开始导出统计信息: " + filePath);

        DicomQueryService.DataStats stats = provider.getDataStats();

        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath, StandardCharsets.UTF_8))) {
            writer.println("{");
            writer.println("  \"dataSource\": \"" + provider.getDataSourceType() + "\",");
            writer.println("  \"timestamp\": \"" + new java.util.Date() + "\",");
            writer.println("  \"statistics\": {");
            writer.println("    \"examCount\": " + stats.getExamCount() + ",");
            writer.println("    \"seriesCount\": " + stats.getSeriesCount() + ",");
            writer.println("    \"imageCount\": " + stats.getImageCount() + ",");
            writer.println("    \"filePathCount\": " + stats.getFilePathCount());
            writer.println("  }");
            writer.println("}");
        }

        LOG.info("统计信息导出完成: " + filePath);
        return filePath;
    }

    // === CSV导出方法 ===

    /**
     * 导出检查到CSV
     */
    private void exportExamsToCsv(DicomDataProvider provider, PrintWriter writer) {
        List<DicomExam> exams = provider.getAllExams();
        SimpleTableConverter.TableData tableData = SimpleTableConverter.createExamTableData(exams);

        // 写入标题行
        writeCsvRow(writer, tableData.getHeaders());

        // 写入数据行
        for (List<String> row : tableData.getRows()) {
            writeCsvRow(writer, row);
        }

        LOG.info("导出了 " + exams.size() + " 个检查");
    }

    /**
     * 导出序列到CSV
     */
    private void exportSeriesToCsv(DicomDataProvider provider, PrintWriter writer) {
        List<DicomSeries> seriesList = provider.getAllSeries();
        SimpleTableConverter.TableData tableData = SimpleTableConverter.createSeriesTableData(seriesList);

        // 写入标题行
        writeCsvRow(writer, tableData.getHeaders());

        // 写入数据行
        for (List<String> row : tableData.getRows()) {
            writeCsvRow(writer, row);
        }

        LOG.info("导出了 " + seriesList.size() + " 个序列");
    }

    /**
     * 导出图像到CSV
     */
    private void exportImagesToCsv(DicomDataProvider provider, PrintWriter writer) {
        List<DicomImage> images = provider.getAllImages();
        SimpleTableConverter.TableData tableData = SimpleTableConverter.createImageTableData(images);

        // 写入标题行
        writeCsvRow(writer, tableData.getHeaders());

        // 写入数据行
        for (List<String> row : tableData.getRows()) {
            writeCsvRow(writer, row);
        }

        LOG.info("导出了 " + images.size() + " 个图像");
    }

    /**
     * 写入CSV行
     */
    private void writeCsvRow(PrintWriter writer, List<String> values) {
        StringBuilder row = new StringBuilder();
        for (int i = 0; i < values.size(); i++) {
            if (i > 0) {
                row.append(",");
            }
            row.append("\"").append(escapeCsvValue(values.get(i))).append("\"");
        }
        writer.println(row.toString());
    }

    /**
     * 转义CSV值
     */
    private String escapeCsvValue(String value) {
        if (value == null) {
            return "";
        }
        return value.replace("\"", "\"\"");
    }

    // === JSON导出方法 ===

    /**
     * 导出检查到JSON
     */
    private void exportExamsToJson(DicomDataProvider provider, PrintWriter writer) {
        List<DicomExam> exams = provider.getAllExams();

        writer.println("{");
        writer.println("  \"exams\": [");

        for (int i = 0; i < exams.size(); i++) {
            DicomExam exam = exams.get(i);
            writer.print("    ");
            writeExamJson(writer, exam);
            if (i < exams.size() - 1) {
                writer.println(",");
            } else {
                writer.println();
            }
        }

        writer.println("  ]");
        writer.println("}");

        LOG.info("导出了 " + exams.size() + " 个检查到JSON");
    }

    /**
     * 导出序列到JSON
     */
    private void exportSeriesToJson(DicomDataProvider provider, PrintWriter writer) {
        List<DicomSeries> seriesList = provider.getAllSeries();

        writer.println("{");
        writer.println("  \"series\": [");

        for (int i = 0; i < seriesList.size(); i++) {
            DicomSeries series = seriesList.get(i);
            writer.print("    ");
            writeSeriesJson(writer, series);
            if (i < seriesList.size() - 1) {
                writer.println(",");
            } else {
                writer.println();
            }
        }

        writer.println("  ]");
        writer.println("}");

        LOG.info("导出了 " + seriesList.size() + " 个序列到JSON");
    }

    /**
     * 导出图像到JSON
     */
    private void exportImagesToJson(DicomDataProvider provider, PrintWriter writer) {
        List<DicomImage> images = provider.getAllImages();

        writer.println("{");
        writer.println("  \"images\": [");

        for (int i = 0; i < images.size(); i++) {
            DicomImage image = images.get(i);
            writer.print("    ");
            writeImageJson(writer, image);
            if (i < images.size() - 1) {
                writer.println(",");
            } else {
                writer.println();
            }
        }

        writer.println("  ]");
        writer.println("}");

        LOG.info("导出了 " + images.size() + " 个图像到JSON");
    }

    /**
     * 写入检查JSON
     */
    private void writeExamJson(PrintWriter writer, DicomExam exam) {
        writer.print("{");
        writer.print("\"id\":\"" + escapeJsonValue(exam.getId()) + "\",");
        writer.print("\"patientID\":\"" + escapeJsonValue(exam.getPatientID()) + "\",");
        writer.print("\"patientName\":\"" + escapeJsonValue(exam.getPatientName()) + "\",");
        writer.print("\"studyDate\":\"" + escapeJsonValue(exam.getStudyDate()) + "\",");
        writer.print("\"studyDescription\":\"" + escapeJsonValue(exam.getStudyDescription()) + "\"");
        writer.print("}");
    }

    /**
     * 写入序列JSON
     */
    private void writeSeriesJson(PrintWriter writer, DicomSeries series) {
        writer.print("{");
        writer.print("\"id\":\"" + escapeJsonValue(series.getId()) + "\",");
        writer.print("\"examId\":\"" + escapeJsonValue(series.getExamId()) + "\",");
        writer.print("\"seriesNumber\":\"" + escapeJsonValue(series.getSeriesNumber()) + "\",");
        writer.print("\"modality\":\"" + escapeJsonValue(series.getModality()) + "\",");
        writer.print("\"seriesDescription\":\"" + escapeJsonValue(series.getSeriesDescription()) + "\"");
        writer.print("}");
    }

    /**
     * 写入图像JSON
     */
    private void writeImageJson(PrintWriter writer, DicomImage image) {
        writer.print("{");
        writer.print("\"id\":\"" + escapeJsonValue(image.getId()) + "\",");
        writer.print("\"seriesId\":\"" + escapeJsonValue(image.getSeriesId()) + "\",");
        writer.print("\"instanceNumber\":\"" + escapeJsonValue(image.getInstanceNumber()) + "\",");
        writer.print("\"filePath\":\"" + escapeJsonValue(image.getFilePath()) + "\",");
        writer.print("\"rows\":" + (image.getRows() != null ? image.getRows() : "null") + ",");
        writer.print("\"columns\":" + (image.getColumns() != null ? image.getColumns() : "null"));
        writer.print("}");
    }

    /**
     * 转义JSON值
     */
    private String escapeJsonValue(String value) {
        if (value == null) {
            return "";
        }
        return value.replace("\\", "\\\\")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }
}
