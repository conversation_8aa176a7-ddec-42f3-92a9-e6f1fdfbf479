package com.ge.med.ct.dicom.table;

import com.ge.med.ct.dicom.model.*;
import com.ge.med.ct.dicom.tag.DicomTagProcessor;

import java.util.*;
import java.util.logging.Logger;

/**
 * 简化的表格转换器
 * 替代复杂的TableDataConverter，提供直接的数据转换功能
 */
public class SimpleTableConverter {
    private static final Logger LOG = Logger.getLogger(SimpleTableConverter.class.getName());

    // 禁止实例化
    private SimpleTableConverter() {
        throw new AssertionError("工具类不应被实例化");
    }

    // === 检查转换 ===

    /**
     * 将检查转换为表格行数据
     */
    public static List<String> convertExamToRow(DicomExam exam) {
        if (exam == null) {
            return Collections.emptyList();
        }

        List<String> row = new ArrayList<>();
        row.add(safeGetValue(exam.getPatientID()));
        row.add(safeGetValue(exam.getPatientName()));
        row.add(safeGetValue(exam.getPatientSex()));
        row.add(safeGetValue(exam.getPatientAge()));
        row.add(DicomTagProcessor.formatDicomDate(exam.getStudyDate()));
        row.add(DicomTagProcessor.formatDicomTime(exam.getStudyTime()));
        row.add(safeGetValue(exam.getStudyDescription()));
        row.add(safeGetValue(exam.getStudyID()));
        row.add(safeGetValue(exam.getAccessionNumber()));

        return row;
    }

    /**
     * 获取检查表格列标题
     */
    public static List<String> getExamColumnHeaders() {
        return Arrays.asList(
            "患者ID",
            "患者姓名", 
            "性别",
            "年龄",
            "检查日期",
            "检查时间",
            "检查描述",
            "检查ID",
            "登记号"
        );
    }

    // === 序列转换 ===

    /**
     * 将序列转换为表格行数据
     */
    public static List<String> convertSeriesToRow(DicomSeries series) {
        if (series == null) {
            return Collections.emptyList();
        }

        List<String> row = new ArrayList<>();
        row.add(safeGetValue(series.getSeriesNumber()));
        row.add(DicomTagProcessor.formatModality(series.getModality()));
        row.add(safeGetValue(series.getSeriesDescription()));
        row.add(DicomTagProcessor.formatDicomDate(series.getSeriesDate()));
        row.add(DicomTagProcessor.formatDicomTime(series.getSeriesTime()));
        row.add(safeGetValue(series.getBodyPartExamined()));
        row.add(safeGetValue(series.getOperatorName()));
        row.add(safeGetValue(series.getSeriesInstanceUID()));

        return row;
    }

    /**
     * 获取序列表格列标题
     */
    public static List<String> getSeriesColumnHeaders() {
        return Arrays.asList(
            "序列号",
            "模态",
            "序列描述",
            "序列日期",
            "序列时间",
            "检查部位",
            "操作员",
            "序列UID"
        );
    }

    // === 图像转换 ===

    /**
     * 将图像转换为表格行数据
     */
    public static List<String> convertImageToRow(DicomImage image) {
        if (image == null) {
            return Collections.emptyList();
        }

        List<String> row = new ArrayList<>();
        row.add(safeGetValue(image.getInstanceNumber()));
        row.add(formatImageSize(image.getRows(), image.getColumns()));
        row.add(DicomTagProcessor.formatWindowValue(String.valueOf(image.getWindowCenter())));
        row.add(DicomTagProcessor.formatWindowValue(String.valueOf(image.getWindowWidth())));
        row.add(DicomTagProcessor.formatSliceThickness(String.valueOf(image.getTagValue("(0018,0050)"))));
        row.add(DicomTagProcessor.formatSliceLocation(String.valueOf(image.getTagValue("(0020,1041)"))));
        row.add(safeGetValue(image.getImageType()));
        row.add(safeGetValue(image.getSopInstanceUID()));

        return row;
    }

    /**
     * 获取图像表格列标题
     */
    public static List<String> getImageColumnHeaders() {
        return Arrays.asList(
            "实例号",
            "图像尺寸",
            "窗位",
            "窗宽", 
            "层厚",
            "层位置",
            "图像类型",
            "SOP实例UID"
        );
    }

    // === 文件信息转换 ===

    /**
     * 将图像文件信息转换为表格行数据
     */
    public static List<String> convertImageFileToRow(DicomImage image) {
        if (image == null) {
            return Collections.emptyList();
        }

        List<String> row = new ArrayList<>();
        row.add(safeGetValue(image.getInstanceNumber()));
        row.add(safeGetValue(image.getFileName()));
        row.add(formatFileSize(image.getFileSize()));
        row.add(safeGetValue(image.getFileType()));
        row.add(safeGetValue(image.getFilePath()));

        return row;
    }

    /**
     * 获取文件信息表格列标题
     */
    public static List<String> getImageFileColumnHeaders() {
        return Arrays.asList(
            "实例号",
            "文件名",
            "文件大小",
            "文件类型",
            "文件路径"
        );
    }

    // === CT特定转换 ===

    /**
     * 将CT图像转换为表格行数据（包含CT特定参数）
     */
    public static List<String> convertCTImageToRow(DicomImage image) {
        if (image == null) {
            return Collections.emptyList();
        }

        List<String> row = new ArrayList<>();
        row.add(safeGetValue(image.getInstanceNumber()));
        row.add(formatImageSize(image.getRows(), image.getColumns()));
        row.add(safeGetValue(image.getTagValue("(0018,0060)"))); // KVP
        row.add(safeGetValue(image.getTagValue("(0018,1151)"))); // mAs
        row.add(safeGetValue(image.getTagValue("(0018,1210)"))); // 卷积核
        row.add(DicomTagProcessor.formatSliceThickness(image.getTagValue("(0018,0050)")));
        row.add(DicomTagProcessor.formatWindowValue(String.valueOf(image.getWindowCenter())));
        row.add(DicomTagProcessor.formatWindowValue(String.valueOf(image.getWindowWidth())));

        return row;
    }

    /**
     * 获取CT图像表格列标题
     */
    public static List<String> getCTImageColumnHeaders() {
        return Arrays.asList(
            "实例号",
            "图像尺寸",
            "管电压(kV)",
            "管电流时间积(mAs)",
            "卷积核",
            "层厚",
            "窗位",
            "窗宽"
        );
    }

    // === 工具方法 ===

    /**
     * 安全获取值，避免null
     */
    private static String safeGetValue(Object value) {
        if (value == null) {
            return "";
        }
        String stringValue = value.toString().trim();
        return DicomTagProcessor.formatSafeText(stringValue);
    }

    /**
     * 格式化图像尺寸
     */
    private static String formatImageSize(Integer rows, Integer columns) {
        if (rows == null || columns == null) {
            return "";
        }
        return rows + " x " + columns;
    }

    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long sizeInBytes) {
        if (sizeInBytes <= 0) {
            return "";
        }

        final String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        double size = sizeInBytes;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.1f %s", size, units[unitIndex]);
    }

    // === 批量转换方法 ===

    /**
     * 批量转换检查列表
     */
    public static List<List<String>> convertExamList(Collection<DicomExam> exams) {
        List<List<String>> result = new ArrayList<>();
        if (exams != null) {
            for (DicomExam exam : exams) {
                result.add(convertExamToRow(exam));
            }
        }
        return result;
    }

    /**
     * 批量转换序列列表
     */
    public static List<List<String>> convertSeriesList(Collection<DicomSeries> seriesList) {
        List<List<String>> result = new ArrayList<>();
        if (seriesList != null) {
            for (DicomSeries series : seriesList) {
                result.add(convertSeriesToRow(series));
            }
        }
        return result;
    }

    /**
     * 批量转换图像列表
     */
    public static List<List<String>> convertImageList(Collection<DicomImage> images) {
        List<List<String>> result = new ArrayList<>();
        if (images != null) {
            for (DicomImage image : images) {
                result.add(convertImageToRow(image));
            }
        }
        return result;
    }

    /**
     * 批量转换CT图像列表
     */
    public static List<List<String>> convertCTImageList(Collection<DicomImage> images) {
        List<List<String>> result = new ArrayList<>();
        if (images != null) {
            for (DicomImage image : images) {
                result.add(convertCTImageToRow(image));
            }
        }
        return result;
    }

    // === 表格数据包装器 ===

    /**
     * 表格数据包装器
     */
    public static class TableData {
        private final List<String> headers;
        private final List<List<String>> rows;

        public TableData(List<String> headers, List<List<String>> rows) {
            this.headers = headers != null ? new ArrayList<>(headers) : new ArrayList<>();
            this.rows = rows != null ? new ArrayList<>(rows) : new ArrayList<>();
        }

        public List<String> getHeaders() {
            return new ArrayList<>(headers);
        }

        public List<List<String>> getRows() {
            return new ArrayList<>(rows);
        }

        public int getRowCount() {
            return rows.size();
        }

        public int getColumnCount() {
            return headers.size();
        }

        public boolean isEmpty() {
            return rows.isEmpty();
        }

        @Override
        public String toString() {
            return String.format("TableData[columns=%d, rows=%d]", getColumnCount(), getRowCount());
        }
    }

    /**
     * 创建检查表格数据
     */
    public static TableData createExamTableData(Collection<DicomExam> exams) {
        return new TableData(getExamColumnHeaders(), convertExamList(exams));
    }

    /**
     * 创建序列表格数据
     */
    public static TableData createSeriesTableData(Collection<DicomSeries> seriesList) {
        return new TableData(getSeriesColumnHeaders(), convertSeriesList(seriesList));
    }

    /**
     * 创建图像表格数据
     */
    public static TableData createImageTableData(Collection<DicomImage> images) {
        return new TableData(getImageColumnHeaders(), convertImageList(images));
    }

    /**
     * 创建CT图像表格数据
     */
    public static TableData createCTImageTableData(Collection<DicomImage> images) {
        return new TableData(getCTImageColumnHeaders(), convertCTImageList(images));
    }
}
