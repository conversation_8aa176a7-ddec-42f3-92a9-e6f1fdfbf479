package com.ge.med.ct.dicom.model;

import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;

import java.util.*;
import java.util.logging.Logger;

/**
 * DICOM检查类（重构版）
 * 移除了与序列的直接关联，消除循环依赖
 * 关系管理由DicomRelationshipManager负责
 */
public class DicomExam {
    private static final Logger LOG = Logger.getLogger(DicomExam.class.getName());

    private final String id;
    private final Map<String, DicomTag> tags;

    // 常用属性缓存
    private String patientID;
    private String patientName;
    private String studyDate;
    private String studyTime;
    private String studyInstanceUID;
    private String studyDescription;

    public DicomExam(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException(DicomMessages.EXAM_ID_EMPTY);
        }
        this.id = id;
        this.tags = new HashMap<>();
        LOG.fine("创建DICOM检查: " + id);
    }

    // === 基本属性访问 ===

    public String getId() {
        return id;
    }

    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }

    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            updateCachedAttributes(tagId, tag.getValueAsString());
            LOG.fine("添加标签 " + tagId + " 到检查 " + id);
        }
    }

    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }

    public Map<String, DicomTag> getTags() {
        return Collections.unmodifiableMap(tags);
    }

    // === 患者信息 ===

    public String getPatientID() {
        return patientID != null ? patientID : getTagValue("(0010,0020)");
    }

    public void setPatientID(String patientID) {
        this.patientID = patientID;
        setTagValue("(0010,0020)", patientID);
    }

    public String getPatientName() {
        return patientName != null ? patientName : getTagValue("(0010,0010)");
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
        setTagValue("(0010,0010)", patientName);
    }

    public String getPatientSex() {
        return getTagValue("(0010,0040)");
    }

    public void setPatientSex(String sex) {
        setTagValue("(0010,0040)", sex);
    }

    public String getPatientAge() {
        return getTagValue("(0010,1010)");
    }

    public void setPatientAge(String age) {
        setTagValue("(0010,1010)", age);
    }

    public String getPatientBirthDate() {
        return getTagValue("(0010,0030)");
    }

    public void setPatientBirthDate(String birthDate) {
        setTagValue("(0010,0030)", birthDate);
    }

    // === 检查信息 ===

    public String getStudyInstanceUID() {
        return studyInstanceUID != null ? studyInstanceUID : getTagValue("(0020,000D)");
    }

    public void setStudyInstanceUID(String studyInstanceUID) {
        this.studyInstanceUID = studyInstanceUID;
        setTagValue("(0020,000D)", studyInstanceUID);
    }

    public String getStudyDate() {
        return studyDate != null ? studyDate : getTagValue("(0008,0020)");
    }

    public void setStudyDate(String studyDate) {
        this.studyDate = studyDate;
        setTagValue("(0008,0020)", studyDate);
    }

    public String getStudyTime() {
        return studyTime != null ? studyTime : getTagValue("(0008,0030)");
    }

    public void setStudyTime(String studyTime) {
        this.studyTime = studyTime;
        setTagValue("(0008,0030)", studyTime);
    }

    public String getStudyDescription() {
        return studyDescription != null ? studyDescription : getTagValue("(0008,1030)");
    }

    public void setStudyDescription(String description) {
        this.studyDescription = description;
        setTagValue("(0008,1030)", description);
    }

    public String getStudyID() {
        return getTagValue("(0020,0010)");
    }

    public void setStudyID(String studyID) {
        setTagValue("(0020,0010)", studyID);
    }

    public String getAccessionNumber() {
        return getTagValue("(0008,0050)");
    }

    public void setAccessionNumber(String accessionNumber) {
        setTagValue("(0008,0050)", accessionNumber);
    }

    // === 工具方法 ===

    private void setTagValue(String tagId, String value) {
        if (value != null) {
            try {
                DicomTag tag = new DicomTag(tagId, value, org.dcm4che3.data.VR.LO);
                addTag(tagId, tag);
            } catch (DicomException e) {
                LOG.warning("设置标签值失败: " + tagId + " = " + value);
            }
        }
    }

    private void updateCachedAttributes(String tagId, String value) {
        switch (tagId) {
            case "(0010,0020)":
                this.patientID = value;
                break;
            case "(0010,0010)":
                this.patientName = value;
                break;
            case "(0020,000D)":
                this.studyInstanceUID = value;
                break;
            case "(0008,0020)":
                this.studyDate = value;
                break;
            case "(0008,0030)":
                this.studyTime = value;
                break;
            case "(0008,1030)":
                this.studyDescription = value;
                break;
        }
    }

    /**
     * 验证检查数据的完整性
     */
    public boolean isValid() {
        return id != null && !id.trim().isEmpty() &&
               getPatientID() != null && !getPatientID().trim().isEmpty() &&
               getStudyInstanceUID() != null && !getStudyInstanceUID().trim().isEmpty();
    }

    /**
     * 获取检查的显示名称
     */
    public String getDisplayName() {
        String patientName = getPatientName();
        String studyDate = getStudyDate();
        String description = getStudyDescription();
        
        StringBuilder displayName = new StringBuilder();
        if (patientName != null && !patientName.isEmpty()) {
            displayName.append(patientName);
        }
        if (studyDate != null && !studyDate.isEmpty()) {
            if (displayName.length() > 0) displayName.append(" - ");
            displayName.append(studyDate);
        }
        if (description != null && !description.isEmpty()) {
            if (displayName.length() > 0) displayName.append(" - ");
            displayName.append(description);
        }
        
        return displayName.length() > 0 ? displayName.toString() : id;
    }

    @Override
    public String toString() {
        return String.format("DicomExam[id=%s, patientID=%s, patientName=%s, studyDate=%s, studyDescription=%s]",
                id, getPatientID(), getPatientName(), getStudyDate(), getStudyDescription());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        DicomExam other = (DicomExam) obj;
        return id.equals(other.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
