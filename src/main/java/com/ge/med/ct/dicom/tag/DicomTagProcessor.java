package com.ge.med.ct.dicom.tag;

import java.text.DecimalFormat;
import java.util.logging.Logger;

/**
 * 统一的DICOM标签处理器
 * 合并了TagFormatter和DicomTagConverter的功能
 * 提供标签值的格式化、转换和验证功能
 */
public class DicomTagProcessor {
    private static final Logger LOG = Logger.getLogger(DicomTagProcessor.class.getName());

    // 数值格式化器
    private static final DecimalFormat INTEGER_FORMAT = new DecimalFormat("#0");
    private static final DecimalFormat FLOAT_FORMAT = new DecimalFormat("#0.##");
    private static final DecimalFormat POSITION_FORMAT = new DecimalFormat("#0.0");
    private static final DecimalFormat PRECISION_FORMAT = new DecimalFormat("#0.000");

    // 禁止实例化
    private DicomTagProcessor() {
        throw new AssertionError("工具类不应被实例化");
    }

    // === 通用格式化方法 ===

    /**
     * 格式化标签值
     */
    public static String formatTagValue(String tagId, Object value) {
        if (value == null) {
            return "";
        }

        String stringValue = getStringValue(value);
        return formatForDisplay(tagId, stringValue);
    }

    /**
     * 针对显示的特殊格式化
     */
    public static String formatForDisplay(String tagId, String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }

        // 根据标签类型进行特殊格式化
        if (DicomTagConstants.Image.IMAGE_POSITION_PATIENT.equals(tagId)) {
            return formatImagePosition(value);
        } else if (DicomTagConstants.Image.PIXEL_SPACING.equals(tagId)) {
            return formatPixelSpacing(value);
        } else if (DicomTagConstants.Image.WINDOW_CENTER.equals(tagId) ||
                   DicomTagConstants.Image.WINDOW_WIDTH.equals(tagId)) {
            return formatWindowValue(value);
        } else if (DicomTagConstants.Image.SLICE_THICKNESS.equals(tagId)) {
            return formatSliceThickness(value);
        } else if (DicomTagConstants.Image.SLICE_LOCATION.equals(tagId)) {
            return formatSliceLocation(value);
        } else if (DicomTagConstants.Patient.PATIENT_NAME.equals(tagId)) {
            return formatPatientName(value);
        } else if (DicomTagConstants.Patient.PATIENT_SEX.equals(tagId)) {
            return formatSex(value);
        } else if (DicomTagConstants.Patient.PATIENT_AGE.equals(tagId)) {
            return formatAge(value);
        } else if (DicomTagConstants.Study.STUDY_DATE.equals(tagId) ||
                   DicomTagConstants.Series.SERIES_DATE.equals(tagId)) {
            return formatDicomDate(value);
        } else if (DicomTagConstants.Study.STUDY_TIME.equals(tagId) ||
                   DicomTagConstants.Series.SERIES_TIME.equals(tagId)) {
            return formatDicomTime(value);
        } else if (DicomTagConstants.Series.MODALITY.equals(tagId)) {
            return formatModality(value);
        }

        return value;
    }

    // === 类型转换方法 ===

    /**
     * 获取字符串值
     */
    public static String getStringValue(Object value) {
        if (value == null) {
            return "";
        }
        return value.toString().trim();
    }

    /**
     * 获取整数值
     */
    public static Integer getIntegerValue(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof Integer) {
            return (Integer) value;
        }
        
        String stringValue = getStringValue(value);
        if (stringValue.isEmpty()) {
            return null;
        }
        
        try {
            return Integer.parseInt(stringValue);
        } catch (NumberFormatException e) {
            LOG.fine("无法解析整数值: " + stringValue);
            return null;
        }
    }

    /**
     * 获取浮点数值
     */
    public static Float getFloatValue(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof Float) {
            return (Float) value;
        }
        
        if (value instanceof Double) {
            return ((Double) value).floatValue();
        }
        
        String stringValue = getStringValue(value);
        if (stringValue.isEmpty()) {
            return null;
        }
        
        try {
            return Float.parseFloat(stringValue);
        } catch (NumberFormatException e) {
            LOG.fine("无法解析浮点数值: " + stringValue);
            return null;
        }
    }

    // === 特殊格式化方法 ===

    /**
     * 格式化图像位置
     */
    public static String formatImagePosition(String position) {
        if (position == null || position.trim().isEmpty()) {
            return "";
        }

        try {
            String[] parts = position.split("\\\\");
            if (parts.length >= 3) {
                float x = Float.parseFloat(parts[0].trim());
                float y = Float.parseFloat(parts[1].trim());
                float z = Float.parseFloat(parts[2].trim());
                return String.format("(%.1f, %.1f, %.1f)", x, y, z);
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析图像位置: " + position);
        }

        return position;
    }

    /**
     * 格式化像素间距
     */
    public static String formatPixelSpacing(String spacing) {
        if (spacing == null || spacing.trim().isEmpty()) {
            return "";
        }

        try {
            String[] parts = spacing.split("\\\\");
            if (parts.length >= 2) {
                float row = Float.parseFloat(parts[0].trim());
                float col = Float.parseFloat(parts[1].trim());
                return String.format("%.2f x %.2f mm", row, col);
            } else if (parts.length == 1) {
                float value = Float.parseFloat(parts[0].trim());
                return FLOAT_FORMAT.format(value) + " mm";
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析像素间距: " + spacing);
        }

        return spacing;
    }

    /**
     * 格式化窗位窗宽值
     */
    public static String formatWindowValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }

        try {
            String[] parts = value.split("\\\\");
            if (parts.length > 0) {
                float firstValue = Float.parseFloat(parts[0].trim());
                return INTEGER_FORMAT.format(firstValue);
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析窗位/窗宽值: " + value);
        }

        return value;
    }

    /**
     * 格式化层厚
     */
    public static String formatSliceThickness(String thickness) {
        if (thickness == null || thickness.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(thickness.trim());
            return FLOAT_FORMAT.format(value) + " mm";
        } catch (NumberFormatException e) {
            LOG.fine("无法解析层厚: " + thickness);
        }

        return thickness;
    }

    /**
     * 格式化层位置
     */
    public static String formatSliceLocation(String location) {
        if (location == null || location.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(location.trim());
            return POSITION_FORMAT.format(value) + " mm";
        } catch (NumberFormatException e) {
            LOG.fine("无法解析层位置: " + location);
        }

        return location;
    }

    /**
     * 格式化DICOM日期
     */
    public static String formatDicomDate(String date) {
        if (date == null || date.trim().isEmpty()) {
            return "";
        }

        String cleanDate = date.trim();
        if (cleanDate.length() >= 8) {
            try {
                String year = cleanDate.substring(0, 4);
                String month = cleanDate.substring(4, 6);
                String day = cleanDate.substring(6, 8);
                return year + "-" + month + "-" + day;
            } catch (StringIndexOutOfBoundsException e) {
                LOG.fine("无法解析DICOM日期: " + date);
            }
        }

        return date;
    }

    /**
     * 格式化DICOM时间
     */
    public static String formatDicomTime(String time) {
        if (time == null || time.trim().isEmpty()) {
            return "";
        }

        String cleanTime = time.trim();
        if (cleanTime.length() >= 6) {
            try {
                String hour = cleanTime.substring(0, 2);
                String minute = cleanTime.substring(2, 4);
                String second = cleanTime.substring(4, 6);
                return hour + ":" + minute + ":" + second;
            } catch (StringIndexOutOfBoundsException e) {
                LOG.fine("无法解析DICOM时间: " + time);
            }
        }

        return time;
    }

    /**
     * 格式化患者姓名
     */
    public static String formatPatientName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return "";
        }

        // DICOM人名格式: LastName^FirstName^MiddleName^Prefix^Suffix
        return name.replace("^", " ").trim();
    }

    /**
     * 格式化年龄
     */
    public static String formatAge(String age) {
        if (age == null || age.trim().isEmpty()) {
            return "";
        }

        String cleanAge = age.trim().toUpperCase();
        if (cleanAge.endsWith("Y")) {
            return cleanAge.substring(0, cleanAge.length() - 1) + " 岁";
        } else if (cleanAge.endsWith("M")) {
            return cleanAge.substring(0, cleanAge.length() - 1) + " 月";
        } else if (cleanAge.endsWith("D")) {
            return cleanAge.substring(0, cleanAge.length() - 1) + " 天";
        }

        return age;
    }

    /**
     * 格式化性别
     */
    public static String formatSex(String sex) {
        if (sex == null || sex.trim().isEmpty()) {
            return "";
        }

        String cleanSex = sex.trim().toUpperCase();
        switch (cleanSex) {
            case "M":
                return "男";
            case "F":
                return "女";
            case "O":
                return "其他";
            default:
                return sex;
        }
    }

    /**
     * 格式化模态
     */
    public static String formatModality(String modality) {
        if (modality == null || modality.trim().isEmpty()) {
            return "";
        }

        String cleanModality = modality.trim().toUpperCase();
        switch (cleanModality) {
            case "CT":
                return "CT";
            case "MR":
                return "MRI";
            case "US":
                return "超声";
            case "XA":
                return "血管造影";
            case "CR":
                return "计算机放射摄影";
            case "DR":
                return "数字放射摄影";
            default:
                return modality;
        }
    }

    /**
     * 格式化整数值
     */
    public static String formatInteger(Object value) {
        if (value == null) {
            return "";
        }

        Integer intValue = getIntegerValue(value);
        if (intValue != null) {
            return INTEGER_FORMAT.format(intValue);
        }

        return getStringValue(value);
    }

    /**
     * 格式化浮点数值
     */
    public static String formatFloat(Object value) {
        if (value == null) {
            return "";
        }

        Float floatValue = getFloatValue(value);
        if (floatValue != null) {
            return FLOAT_FORMAT.format(floatValue);
        }

        return getStringValue(value);
    }

    /**
     * 格式化为表格显示的安全文本
     */
    public static String formatSafeText(String text) {
        if (text == null) {
            return "";
        }

        // 移除换行符和制表符
        String safeText = text.replaceAll("[\r\n\t]", " ");

        // 限制长度
        return truncateText(safeText.trim(), 50);
    }

    /**
     * 截断长文本
     */
    public static String truncateText(String text, int maxLength) {
        if (text == null || text.length() <= maxLength) {
            return text;
        }

        return text.substring(0, maxLength - 3) + "...";
    }
}
