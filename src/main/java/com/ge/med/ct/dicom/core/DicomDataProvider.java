package com.ge.med.ct.dicom.core;

import com.ge.med.ct.dicom.model.*;

import java.util.List;
import java.util.Map;

/**
 * DICOM数据提供者接口（重构版）
 * 组合了多个小接口，遵循接口隔离原则
 */
public interface DicomDataProvider extends 
    DicomQueryProvider, 
    DicomFilePathProvider, 
    DicomMetadataProvider, 
    DicomStatsProvider {
    
    /**
     * 加载数据
     */
    void loadData() throws Exception;
    
    /**
     * 刷新数据
     */
    void refreshData() throws Exception;
    
    /**
     * 清除数据
     */
    void clearData();
    
    /**
     * 获取数据源类型
     */
    DataSourceType getDataSourceType();
    
    /**
     * 数据源类型枚举
     */
    enum DataSourceType {
        FILE_SYSTEM("文件系统"),
        PESI("PESI数据库");
        
        private final String displayName;
        
        DataSourceType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}

/**
 * 基本查询接口
 */
interface DicomQueryProvider {
    List<DicomExam> getAllExams();
    DicomExam getExam(String examId);
    List<DicomSeries> getSeriesForExam(String examId);
    DicomSeries getSeries(String seriesId);
    List<DicomImage> getImagesForSeries(String seriesId);
    DicomImage getImage(String imageId);
}

/**
 * 文件路径接口
 */
interface DicomFilePathProvider {
    String getImageFilePath(String imageId);
    Map<String, String> getImageFilePaths(List<String> imageIds);
    DicomImage getImageByFilePath(String filePath);
}

/**
 * 元数据接口
 */
interface DicomMetadataProvider {
    String getTagValue(String imageId, String tagId);
    Map<String, String> getImageMetadata(String imageId);
}

/**
 * 统计信息接口
 */
interface DicomStatsProvider {
    DicomQueryService.DataStats getDataStats();
    int getExamCount();
    int getSeriesCount();
    int getImageCount();
}
