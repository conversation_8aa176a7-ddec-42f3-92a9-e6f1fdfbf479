package com.ge.med.ct.dicom.service;

import com.ge.med.ct.dicom.source.DicomDataSource;
import com.ge.med.ct.dicom.provider.DicomDataProvider;
import com.ge.med.ct.dicom.query.DicomQueryService;

/**
 * DICOM数据服务接口（重构版）
 * 提供统一的数据加载和管理服务
 */
public interface DicomDataService {

    /**
     * 加载DICOM数据
     * @param source 数据源配置
     * @param callback 进度回调
     */
    void loadDicomData(DicomDataSource source, ProgressCallback callback);

    /**
     * 获取数据提供者
     * @return 当前的数据提供者实例
     */
    DicomDataProvider getDataProvider();

    /**
     * 获取当前数据源类型
     * @return 当前使用的数据源类型
     */
    DicomDataProvider.DataSourceType getCurrentSourceType();

    /**
     * 刷新数据
     * 重新加载当前数据源的数据
     */
    void refreshData();

    /**
     * 获取数据统计信息
     * @return 数据统计信息
     */
    DicomQueryService.DataStats getDataStats();

    /**
     * 检查服务是否已初始化
     * @return 是否已初始化
     */
    boolean isInitialized();

    /**
     * 检查是否正在加载数据
     * @return 是否正在加载
     */
    boolean isLoading();

    /**
     * 关闭服务
     * 释放所有资源
     */
    void close();

    /**
     * 进度回调接口
     */
    interface ProgressCallback {
        /**
         * 进度更新回调
         * @param info 进度信息
         */
        void onProgress(ProgressInfo info);

        /**
         * 错误回调
         * @param error 错误信息
         */
        void onError(String error);

        /**
         * 完成回调
         */
        void onComplete();
    }

    /**
     * 进度信息
     */
    class ProgressInfo {
        private final String message;
        private final int percentage;
        private final long timestamp;

        public ProgressInfo(String message, int percentage) {
            this.message = message;
            this.percentage = Math.max(0, Math.min(100, percentage));
            this.timestamp = System.currentTimeMillis();
        }

        public String getMessage() {
            return message;
        }

        public int getPercentage() {
            return percentage;
        }

        public long getTimestamp() {
            return timestamp;
        }

        @Override
        public String toString() {
            return String.format("ProgressInfo{message='%s', percentage=%d%%}", message, percentage);
        }
    }
}
