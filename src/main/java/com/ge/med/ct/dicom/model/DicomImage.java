package com.ge.med.ct.dicom.model;

import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;

import java.util.*;
import java.util.logging.Logger;

/**
 * DICOM图像类（重构版）
 * 移除了与序列的直接关联，消除循环依赖
 * 使用单向引用到序列ID，关系管理由DicomRelationshipManager负责
 * 合并了DicomFileModel的功能，减少重复代码
 */
public class DicomImage {
    private static final Logger LOG = Logger.getLogger(DicomImage.class.getName());

    private final String id;
    private final String seriesId; // 单向引用到序列ID
    private final Map<String, DicomTag> tags;

    // 文件相关属性（合并自DicomFileModel）
    private String filePath;
    private String fileName;
    private long fileSize;
    private String fileType;

    // 常用属性缓存
    private String sopInstanceUID;
    private String instanceNumber;
    private String imageType;
    private Integer rows;
    private Integer columns;

    // 图像位置和方向
    private float[] imagePosition;
    private float[] imageOrientation;
    private float[] pixelSpacing;

    // 窗宽窗位
    private Float windowCenter;
    private Float windowWidth;
    private Float rescaleSlope;
    private Float rescaleIntercept;

    public DicomImage(String id, String seriesId) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException(DicomMessages.IMAGE_ID_EMPTY);
        }
        if (seriesId == null || seriesId.trim().isEmpty()) {
            throw new DicomException("序列ID不能为空");
        }
        
        this.id = id;
        this.seriesId = seriesId;
        this.tags = new HashMap<>();
        LOG.fine("创建DICOM图像: " + id + " (序列: " + seriesId + ")");
    }

    // === 基本属性访问 ===

    public String getId() {
        return id;
    }

    public String getSeriesId() {
        return seriesId;
    }

    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }

    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            updateCachedAttributes(tagId, tag.getValueAsString());
            LOG.fine("添加标签 " + tagId + " 到图像 " + id);
        }
    }

    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }

    public Map<String, DicomTag> getTags() {
        return Collections.unmodifiableMap(tags);
    }

    // === 文件相关属性 ===

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
        if (filePath != null) {
            // 自动提取文件名
            int lastSeparator = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
            if (lastSeparator >= 0 && lastSeparator < filePath.length() - 1) {
                this.fileName = filePath.substring(lastSeparator + 1);
            }
            
            // 自动提取文件类型
            int lastDot = filePath.lastIndexOf('.');
            if (lastDot >= 0 && lastDot < filePath.length() - 1) {
                this.fileType = filePath.substring(lastDot + 1).toLowerCase();
            }
        }
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    // === 图像信息 ===

    public String getSopInstanceUID() {
        return sopInstanceUID != null ? sopInstanceUID : getTagValue("(0008,0018)");
    }

    public void setSopInstanceUID(String sopInstanceUID) {
        this.sopInstanceUID = sopInstanceUID;
        setTagValue("(0008,0018)", sopInstanceUID);
    }

    public String getInstanceNumber() {
        return instanceNumber != null ? instanceNumber : getTagValue("(0020,0013)");
    }

    public void setInstanceNumber(String instanceNumber) {
        this.instanceNumber = instanceNumber;
        setTagValue("(0020,0013)", instanceNumber);
    }

    public String getImageType() {
        return imageType != null ? imageType : getTagValue("(0008,0008)");
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
        setTagValue("(0008,0008)", imageType);
    }

    public Integer getRows() {
        return rows != null ? rows : parseInteger(getTagValue("(0028,0010)"));
    }

    public void setRows(Integer rows) {
        this.rows = rows;
        if (rows != null) {
            setTagValue("(0028,0010)", rows.toString());
        }
    }

    public Integer getColumns() {
        return columns != null ? columns : parseInteger(getTagValue("(0028,0011)"));
    }

    public void setColumns(Integer columns) {
        this.columns = columns;
        if (columns != null) {
            setTagValue("(0028,0011)", columns.toString());
        }
    }

    // === 图像位置和方向 ===

    public float[] getImagePosition() {
        return imagePosition != null ? imagePosition.clone() : null;
    }

    public void setImagePosition(float[] imagePosition) {
        this.imagePosition = imagePosition != null ? imagePosition.clone() : null;
    }

    public float[] getImageOrientation() {
        return imageOrientation != null ? imageOrientation.clone() : null;
    }

    public void setImageOrientation(float[] imageOrientation) {
        this.imageOrientation = imageOrientation != null ? imageOrientation.clone() : null;
    }

    public float[] getPixelSpacing() {
        return pixelSpacing != null ? pixelSpacing.clone() : null;
    }

    public void setPixelSpacing(float[] pixelSpacing) {
        this.pixelSpacing = pixelSpacing != null ? pixelSpacing.clone() : null;
    }

    // === 窗宽窗位 ===

    public Float getWindowCenter() {
        return windowCenter != null ? windowCenter : parseFloat(getTagValue("(0028,1050)"));
    }

    public void setWindowCenter(Float windowCenter) {
        this.windowCenter = windowCenter;
        if (windowCenter != null) {
            setTagValue("(0028,1050)", windowCenter.toString());
        }
    }

    public Float getWindowWidth() {
        return windowWidth != null ? windowWidth : parseFloat(getTagValue("(0028,1051)"));
    }

    public void setWindowWidth(Float windowWidth) {
        this.windowWidth = windowWidth;
        if (windowWidth != null) {
            setTagValue("(0028,1051)", windowWidth.toString());
        }
    }

    public Float getRescaleSlope() {
        return rescaleSlope != null ? rescaleSlope : parseFloat(getTagValue("(0028,1053)"));
    }

    public void setRescaleSlope(Float rescaleSlope) {
        this.rescaleSlope = rescaleSlope;
        if (rescaleSlope != null) {
            setTagValue("(0028,1053)", rescaleSlope.toString());
        }
    }

    public Float getRescaleIntercept() {
        return rescaleIntercept != null ? rescaleIntercept : parseFloat(getTagValue("(0028,1052)"));
    }

    public void setRescaleIntercept(Float rescaleIntercept) {
        this.rescaleIntercept = rescaleIntercept;
        if (rescaleIntercept != null) {
            setTagValue("(0028,1052)", rescaleIntercept.toString());
        }
    }

    // === 工具方法 ===

    private void setTagValue(String tagId, String value) {
        if (value != null) {
            try {
                DicomTag tag = new DicomTag(tagId, value, org.dcm4che3.data.VR.LO);
                addTag(tagId, tag);
            } catch (DicomException e) {
                LOG.warning("设置标签值失败: " + tagId + " = " + value);
            }
        }
    }

    private void updateCachedAttributes(String tagId, String value) {
        switch (tagId) {
            case "(0008,0018)":
                this.sopInstanceUID = value;
                break;
            case "(0020,0013)":
                this.instanceNumber = value;
                break;
            case "(0008,0008)":
                this.imageType = value;
                break;
            case "(0028,0010)":
                this.rows = parseInteger(value);
                break;
            case "(0028,0011)":
                this.columns = parseInteger(value);
                break;
            case "(0028,1050)":
                this.windowCenter = parseFloat(value);
                break;
            case "(0028,1051)":
                this.windowWidth = parseFloat(value);
                break;
            case "(0028,1052)":
                this.rescaleIntercept = parseFloat(value);
                break;
            case "(0028,1053)":
                this.rescaleSlope = parseFloat(value);
                break;
        }
    }

    private Integer parseInteger(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Float parseFloat(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Float.parseFloat(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 清除像素数据，释放内存
     */
    public void clearPixelData() {
        tags.remove("(7FE0,0010)"); // Pixel Data tag
    }

    /**
     * 验证图像数据的完整性
     */
    public boolean isValid() {
        return id != null && !id.trim().isEmpty() &&
               seriesId != null && !seriesId.trim().isEmpty() &&
               getSopInstanceUID() != null && !getSopInstanceUID().trim().isEmpty();
    }

    /**
     * 获取图像的显示名称
     */
    public String getDisplayName() {
        String instanceNumber = getInstanceNumber();
        String imageType = getImageType();
        
        if (instanceNumber != null && !instanceNumber.isEmpty()) {
            return "Image " + instanceNumber + (imageType != null ? " (" + imageType + ")" : "");
        }
        
        if (fileName != null && !fileName.isEmpty()) {
            return fileName;
        }
        
        return id;
    }

    @Override
    public String toString() {
        return String.format("DicomImage[id=%s, seriesId=%s, sopInstanceUID=%s, instanceNumber=%s, rows=%s, columns=%s, filePath=%s]",
                id, seriesId, getSopInstanceUID(), getInstanceNumber(), getRows(), getColumns(), filePath);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        DicomImage other = (DicomImage) obj;
        return id.equals(other.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
