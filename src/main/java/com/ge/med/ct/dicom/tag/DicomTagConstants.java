package com.ge.med.ct.dicom.tag;

import org.dcm4che3.data.VR;
import org.dcm4che3.data.Tag;

import java.util.*;

/**
 * DICOM标签常量类(重构版)
 * 集中管理常用的DICOM标签ID常量
 */
public final class DicomTagConstants {

    // 禁止实例化
    private DicomTagConstants() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 患者相关标签
     */
    public static final class Patient {
        public static final String PATIENT_ID = "(0010,0020)";
        public static final String PATIENT_NAME = "(0010,0010)";
        public static final String PATIENT_BIRTH_DATE = "(0010,0030)";
        public static final String PATIENT_SEX = "(0010,0040)";
        public static final String PATIENT_AGE = "(0010,1010)";
        public static final String PATIENT_WEIGHT = "(0010,1030)";
        public static final String PATIENT_SIZE = "(0010,1020)";
    }

    /**
     * 检查相关标签
     */
    public static final class Study {
        public static final String STUDY_INSTANCE_UID = "(0020,000D)";
        public static final String STUDY_DATE = "(0008,0020)";
        public static final String STUDY_TIME = "(0008,0030)";
        public static final String STUDY_DESCRIPTION = "(0008,1030)";
        public static final String STUDY_ID = "(0020,0010)";
        public static final String ACCESSION_NUMBER = "(0008,0050)";
        public static final String REFERRING_PHYSICIAN_NAME = "(0008,0090)";
        public static final String PROTOCOL_NAME = "(0018,1030)";
    }

    /**
     * 序列相关标签
     */
    public static final class Series {
        public static final String SERIES_INSTANCE_UID = "(0020,000E)";
        public static final String SERIES_NUMBER = "(0020,0011)";
        public static final String SERIES_DESCRIPTION = "(0008,103E)";
        public static final String SERIES_DATE = "(0008,0021)";
        public static final String SERIES_TIME = "(0008,0031)";
        public static final String MODALITY = "(0008,0060)";
        public static final String BODY_PART_EXAMINED = "(0018,0015)";
        public static final String OPERATOR_NAME = "(0008,1070)";
    }

    /**
     * 图像相关标签
     */
    public static final class Image {
        public static final String SOP_INSTANCE_UID = "(0008,0018)";
        public static final String INSTANCE_NUMBER = "(0020,0013)";
        public static final String IMAGE_TYPE = "(0008,0008)";
        public static final String ROWS = "(0028,0010)";
        public static final String COLUMNS = "(0028,0011)";
        public static final String BITS_ALLOCATED = "(0028,0100)";
        public static final String BITS_STORED = "(0028,0101)";
        public static final String HIGH_BIT = "(0028,0102)";
        public static final String PIXEL_REPRESENTATION = "(0028,0103)";
        public static final String SAMPLES_PER_PIXEL = "(0028,0002)";
        public static final String PHOTOMETRIC_INTERPRETATION = "(0028,0004)";
        public static final String PIXEL_DATA = "(7FE0,0010)";

        // 图像位置和方向
        public static final String IMAGE_POSITION_PATIENT = "(0020,0032)";
        public static final String IMAGE_ORIENTATION_PATIENT = "(0020,0037)";
        public static final String PIXEL_SPACING = "(0028,0030)";
        public static final String SLICE_THICKNESS = "(0018,0050)";
        public static final String SPACING_BETWEEN_SLICES = "(0018,0088)";
        public static final String SLICE_LOCATION = "(0020,1041)";

        // 窗宽窗位
        public static final String WINDOW_CENTER = "(0028,1050)";
        public static final String WINDOW_WIDTH = "(0028,1051)";
        public static final String RESCALE_INTERCEPT = "(0028,1052)";
        public static final String RESCALE_SLOPE = "(0028,1053)";
    }

    /**
     * CT相关标签
     */
    public static final class CT {
        public static final String KVP = "(0018,0060)";
        public static final String EXPOSURE_TIME = "(0018,1150)";
        public static final String X_RAY_TUBE_CURRENT = "(0018,1151)";
        public static final String EXPOSURE = "(0018,1152)";
        public static final String FILTER_TYPE = "(0018,1160)";
        public static final String GENERATOR_POWER = "(0018,1170)";
        public static final String FOCAL_SPOT = "(0018,1190)";
        public static final String CONVOLUTION_KERNEL = "(0018,1210)";
        public static final String REVOLUTION_TIME = "(0018,9305)";
        public static final String SINGLE_COLLIMATION_WIDTH = "(0018,9306)";
        public static final String TOTAL_COLLIMATION_WIDTH = "(0018,9307)";
        public static final String PITCH_FACTOR = "(0018,9311)";
        public static final String DATA_COLLECTION_DIAMETER = "(0018,0090)";
        public static final String RECONSTRUCTION_DIAMETER = "(0018,1100)";
        public static final String GANTRY_DETECTOR_TILT = "(0018,1120)";
        public static final String TABLE_HEIGHT = "(0018,1130)";
        public static final String TABLE_TRAVERSE = "(0018,1131)";
        public static final String TABLE_VERTICAL_INCREMENT = "(0018,1135)";
        public static final String TABLE_LATERAL_INCREMENT = "(0018,1136)";
        public static final String TABLE_LONGITUDINAL_INCREMENT = "(0018,1137)";
        public static final String TABLE_ANGLE = "(0018,1138)";
    }

    /**
     * 设备相关标签
     */
    public static final class Equipment {
        public static final String MANUFACTURER = "(0008,0070)";
        public static final String INSTITUTION_NAME = "(0008,0080)";
        public static final String STATION_NAME = "(0008,1010)";
        public static final String INSTITUTIONAL_DEPARTMENT_NAME = "(0008,1040)";
        public static final String MANUFACTURER_MODEL_NAME = "(0008,1090)";
        public static final String DEVICE_SERIAL_NUMBER = "(0018,1000)";
        public static final String SOFTWARE_VERSION = "(0018,1020)";
    }

    // 必要的标签ID（用于验证DICOM文件完整性）
    public static final String[] ESSENTIAL_TAG_IDS = {
            Patient.PATIENT_ID,
            Patient.PATIENT_NAME,
            Study.STUDY_INSTANCE_UID,
            Study.STUDY_DATE,
            Series.SERIES_INSTANCE_UID,
            Series.MODALITY,
            Image.SOP_INSTANCE_UID,
            Image.ROWS,
            Image.COLUMNS
    };

    // 常用显示标签
    public static final String[] COMMON_DISPLAY_TAG_IDS = {
            Patient.PATIENT_ID,
            Patient.PATIENT_NAME,
            Patient.PATIENT_SEX,
            Patient.PATIENT_AGE,
            Study.STUDY_DATE,
            Study.STUDY_DESCRIPTION,
            Series.SERIES_NUMBER,
            Series.SERIES_DESCRIPTION,
            Series.MODALITY,
            Image.INSTANCE_NUMBER,
            Image.ROWS,
            Image.COLUMNS,
            Image.SLICE_THICKNESS,
            CT.KVP,
            CT.CONVOLUTION_KERNEL
    };

    // 标签名称映射
    private static final Map<String, String> TAG_NAMES = new HashMap<>();
    private static final Map<String, VR> TAG_VRS = new HashMap<>();

    static {
        initializeTagMappings();
    }

    /**
     * 初始化标签映射
     */
    private static void initializeTagMappings() {
        // 患者标签
        TAG_NAMES.put(Patient.PATIENT_ID, "Patient ID");
        TAG_NAMES.put(Patient.PATIENT_NAME, "Patient Name");
        TAG_NAMES.put(Patient.PATIENT_BIRTH_DATE, "Patient Birth Date");
        TAG_NAMES.put(Patient.PATIENT_SEX, "Patient Sex");
        TAG_NAMES.put(Patient.PATIENT_AGE, "Patient Age");
        TAG_NAMES.put(Patient.PATIENT_WEIGHT, "Patient Weight");
        TAG_NAMES.put(Patient.PATIENT_SIZE, "Patient Size");

        // 检查标签
        TAG_NAMES.put(Study.STUDY_INSTANCE_UID, "Study Instance UID");
        TAG_NAMES.put(Study.STUDY_DATE, "Study Date");
        TAG_NAMES.put(Study.STUDY_TIME, "Study Time");
        TAG_NAMES.put(Study.STUDY_DESCRIPTION, "Study Description");
        TAG_NAMES.put(Study.STUDY_ID, "Study ID");
        TAG_NAMES.put(Study.ACCESSION_NUMBER, "Accession Number");
        TAG_NAMES.put(Study.REFERRING_PHYSICIAN_NAME, "Referring Physician Name");
        TAG_NAMES.put(Study.PROTOCOL_NAME, "Protocol Name");

        // 序列标签
        TAG_NAMES.put(Series.SERIES_INSTANCE_UID, "Series Instance UID");
        TAG_NAMES.put(Series.SERIES_NUMBER, "Series Number");
        TAG_NAMES.put(Series.SERIES_DESCRIPTION, "Series Description");
        TAG_NAMES.put(Series.SERIES_DATE, "Series Date");
        TAG_NAMES.put(Series.SERIES_TIME, "Series Time");
        TAG_NAMES.put(Series.MODALITY, "Modality");
        TAG_NAMES.put(Series.BODY_PART_EXAMINED, "Body Part Examined");
        TAG_NAMES.put(Series.OPERATOR_NAME, "Operator Name");

        // 图像标签
        TAG_NAMES.put(Image.SOP_INSTANCE_UID, "SOP Instance UID");
        TAG_NAMES.put(Image.INSTANCE_NUMBER, "Instance Number");
        TAG_NAMES.put(Image.IMAGE_TYPE, "Image Type");
        TAG_NAMES.put(Image.ROWS, "Rows");
        TAG_NAMES.put(Image.COLUMNS, "Columns");
        TAG_NAMES.put(Image.BITS_ALLOCATED, "Bits Allocated");
        TAG_NAMES.put(Image.BITS_STORED, "Bits Stored");
        TAG_NAMES.put(Image.HIGH_BIT, "High Bit");
        TAG_NAMES.put(Image.PIXEL_REPRESENTATION, "Pixel Representation");
        TAG_NAMES.put(Image.SAMPLES_PER_PIXEL, "Samples per Pixel");
        TAG_NAMES.put(Image.PHOTOMETRIC_INTERPRETATION, "Photometric Interpretation");
        TAG_NAMES.put(Image.PIXEL_DATA, "Pixel Data");
        TAG_NAMES.put(Image.IMAGE_POSITION_PATIENT, "Image Position Patient");
        TAG_NAMES.put(Image.IMAGE_ORIENTATION_PATIENT, "Image Orientation Patient");
        TAG_NAMES.put(Image.PIXEL_SPACING, "Pixel Spacing");
        TAG_NAMES.put(Image.SLICE_THICKNESS, "Slice Thickness");
        TAG_NAMES.put(Image.SPACING_BETWEEN_SLICES, "Spacing Between Slices");
        TAG_NAMES.put(Image.SLICE_LOCATION, "Slice Location");
        TAG_NAMES.put(Image.WINDOW_CENTER, "Window Center");
        TAG_NAMES.put(Image.WINDOW_WIDTH, "Window Width");
        TAG_NAMES.put(Image.RESCALE_INTERCEPT, "Rescale Intercept");
        TAG_NAMES.put(Image.RESCALE_SLOPE, "Rescale Slope");

        // CT标签
        TAG_NAMES.put(CT.KVP, "KVP");
        TAG_NAMES.put(CT.EXPOSURE_TIME, "Exposure Time");
        TAG_NAMES.put(CT.X_RAY_TUBE_CURRENT, "X-Ray Tube Current");
        TAG_NAMES.put(CT.EXPOSURE, "Exposure");
        TAG_NAMES.put(CT.FILTER_TYPE, "Filter Type");
        TAG_NAMES.put(CT.GENERATOR_POWER, "Generator Power");
        TAG_NAMES.put(CT.FOCAL_SPOT, "Focal Spot");
        TAG_NAMES.put(CT.CONVOLUTION_KERNEL, "Convolution Kernel");
        TAG_NAMES.put(CT.REVOLUTION_TIME, "Revolution Time");
        TAG_NAMES.put(CT.SINGLE_COLLIMATION_WIDTH, "Single Collimation Width");
        TAG_NAMES.put(CT.TOTAL_COLLIMATION_WIDTH, "Total Collimation Width");
        TAG_NAMES.put(CT.PITCH_FACTOR, "Pitch Factor");
        TAG_NAMES.put(CT.DATA_COLLECTION_DIAMETER, "Data Collection Diameter");
        TAG_NAMES.put(CT.RECONSTRUCTION_DIAMETER, "Reconstruction Diameter");
        TAG_NAMES.put(CT.GANTRY_DETECTOR_TILT, "Gantry/Detector Tilt");
        TAG_NAMES.put(CT.TABLE_HEIGHT, "Table Height");
        TAG_NAMES.put(CT.TABLE_TRAVERSE, "Table Traverse");
        TAG_NAMES.put(CT.TABLE_VERTICAL_INCREMENT, "Table Vertical Increment");
        TAG_NAMES.put(CT.TABLE_LATERAL_INCREMENT, "Table Lateral Increment");
        TAG_NAMES.put(CT.TABLE_LONGITUDINAL_INCREMENT, "Table Longitudinal Increment");
        TAG_NAMES.put(CT.TABLE_ANGLE, "Table Angle");

        // 设备标签
        TAG_NAMES.put(Equipment.MANUFACTURER, "Manufacturer");
        TAG_NAMES.put(Equipment.INSTITUTION_NAME, "Institution Name");
        TAG_NAMES.put(Equipment.STATION_NAME, "Station Name");
        TAG_NAMES.put(Equipment.INSTITUTIONAL_DEPARTMENT_NAME, "Institutional Department Name");
        TAG_NAMES.put(Equipment.MANUFACTURER_MODEL_NAME, "Manufacturer Model Name");
        TAG_NAMES.put(Equipment.DEVICE_SERIAL_NUMBER, "Device Serial Number");
        TAG_NAMES.put(Equipment.SOFTWARE_VERSION, "Software Version");

        // 初始化VR映射
        initializeVRMappings();
    }

    /**
     * 初始化VR映射
     */
    private static void initializeVRMappings() {
        // 患者标签VR
        TAG_VRS.put(Patient.PATIENT_ID, VR.LO);
        TAG_VRS.put(Patient.PATIENT_NAME, VR.PN);
        TAG_VRS.put(Patient.PATIENT_BIRTH_DATE, VR.DA);
        TAG_VRS.put(Patient.PATIENT_SEX, VR.CS);
        TAG_VRS.put(Patient.PATIENT_AGE, VR.AS);
        TAG_VRS.put(Patient.PATIENT_WEIGHT, VR.DS);
        TAG_VRS.put(Patient.PATIENT_SIZE, VR.DS);

        // 检查标签VR
        TAG_VRS.put(Study.STUDY_INSTANCE_UID, VR.UI);
        TAG_VRS.put(Study.STUDY_DATE, VR.DA);
        TAG_VRS.put(Study.STUDY_TIME, VR.TM);
        TAG_VRS.put(Study.STUDY_DESCRIPTION, VR.LO);
        TAG_VRS.put(Study.STUDY_ID, VR.SH);
        TAG_VRS.put(Study.ACCESSION_NUMBER, VR.SH);
        TAG_VRS.put(Study.REFERRING_PHYSICIAN_NAME, VR.PN);
        TAG_VRS.put(Study.PROTOCOL_NAME, VR.LO);

        // 序列标签VR
        TAG_VRS.put(Series.SERIES_INSTANCE_UID, VR.UI);
        TAG_VRS.put(Series.SERIES_NUMBER, VR.IS);
        TAG_VRS.put(Series.SERIES_DESCRIPTION, VR.LO);
        TAG_VRS.put(Series.SERIES_DATE, VR.DA);
        TAG_VRS.put(Series.SERIES_TIME, VR.TM);
        TAG_VRS.put(Series.MODALITY, VR.CS);
        TAG_VRS.put(Series.BODY_PART_EXAMINED, VR.CS);
        TAG_VRS.put(Series.OPERATOR_NAME, VR.PN);

        // 图像标签VR
        TAG_VRS.put(Image.SOP_INSTANCE_UID, VR.UI);
        TAG_VRS.put(Image.INSTANCE_NUMBER, VR.IS);
        TAG_VRS.put(Image.IMAGE_TYPE, VR.CS);
        TAG_VRS.put(Image.ROWS, VR.US);
        TAG_VRS.put(Image.COLUMNS, VR.US);
        TAG_VRS.put(Image.BITS_ALLOCATED, VR.US);
        TAG_VRS.put(Image.BITS_STORED, VR.US);
        TAG_VRS.put(Image.HIGH_BIT, VR.US);
        TAG_VRS.put(Image.PIXEL_REPRESENTATION, VR.US);
        TAG_VRS.put(Image.SAMPLES_PER_PIXEL, VR.US);
        TAG_VRS.put(Image.PHOTOMETRIC_INTERPRETATION, VR.CS);
        TAG_VRS.put(Image.PIXEL_DATA, VR.OW);
        TAG_VRS.put(Image.IMAGE_POSITION_PATIENT, VR.DS);
        TAG_VRS.put(Image.IMAGE_ORIENTATION_PATIENT, VR.DS);
        TAG_VRS.put(Image.PIXEL_SPACING, VR.DS);
        TAG_VRS.put(Image.SLICE_THICKNESS, VR.DS);
        TAG_VRS.put(Image.SPACING_BETWEEN_SLICES, VR.DS);
        TAG_VRS.put(Image.SLICE_LOCATION, VR.DS);
        TAG_VRS.put(Image.WINDOW_CENTER, VR.DS);
        TAG_VRS.put(Image.WINDOW_WIDTH, VR.DS);
        TAG_VRS.put(Image.RESCALE_INTERCEPT, VR.DS);
        TAG_VRS.put(Image.RESCALE_SLOPE, VR.DS);

        // CT标签VR
        TAG_VRS.put(CT.KVP, VR.DS);
        TAG_VRS.put(CT.EXPOSURE_TIME, VR.IS);
        TAG_VRS.put(CT.X_RAY_TUBE_CURRENT, VR.IS);
        TAG_VRS.put(CT.EXPOSURE, VR.IS);
        TAG_VRS.put(CT.FILTER_TYPE, VR.SH);
        TAG_VRS.put(CT.GENERATOR_POWER, VR.IS);
        TAG_VRS.put(CT.FOCAL_SPOT, VR.DS);
        TAG_VRS.put(CT.CONVOLUTION_KERNEL, VR.SH);
        TAG_VRS.put(CT.REVOLUTION_TIME, VR.DS);
        TAG_VRS.put(CT.SINGLE_COLLIMATION_WIDTH, VR.DS);
        TAG_VRS.put(CT.TOTAL_COLLIMATION_WIDTH, VR.DS);
        TAG_VRS.put(CT.PITCH_FACTOR, VR.DS);
        TAG_VRS.put(CT.DATA_COLLECTION_DIAMETER, VR.DS);
        TAG_VRS.put(CT.RECONSTRUCTION_DIAMETER, VR.DS);
        TAG_VRS.put(CT.GANTRY_DETECTOR_TILT, VR.DS);
        TAG_VRS.put(CT.TABLE_HEIGHT, VR.DS);
        TAG_VRS.put(CT.TABLE_TRAVERSE, VR.DS);
        TAG_VRS.put(CT.TABLE_VERTICAL_INCREMENT, VR.DS);
        TAG_VRS.put(CT.TABLE_LATERAL_INCREMENT, VR.DS);
        TAG_VRS.put(CT.TABLE_LONGITUDINAL_INCREMENT, VR.DS);
        TAG_VRS.put(CT.TABLE_ANGLE, VR.DS);

        // 设备标签VR
        TAG_VRS.put(Equipment.MANUFACTURER, VR.LO);
        TAG_VRS.put(Equipment.INSTITUTION_NAME, VR.LO);
        TAG_VRS.put(Equipment.STATION_NAME, VR.SH);
        TAG_VRS.put(Equipment.INSTITUTIONAL_DEPARTMENT_NAME, VR.LO);
        TAG_VRS.put(Equipment.MANUFACTURER_MODEL_NAME, VR.LO);
        TAG_VRS.put(Equipment.DEVICE_SERIAL_NUMBER, VR.LO);
        TAG_VRS.put(Equipment.SOFTWARE_VERSION, VR.LO);
    }

    /**
     * 获取标签名称
     */
    public static String getTagName(String tagId) {
        if (tagId == null) {
            return "Unknown";
        }

        String name = TAG_NAMES.get(tagId);
        return name != null ? name : "Tag-" + tagId;
    }

    /**
     * 获取标签VR
     */
    public static VR getTagVR(String tagId) {
        if (tagId == null) {
            return VR.UN;
        }

        VR vr = TAG_VRS.get(tagId);
        return vr != null ? vr : VR.UN;
    }

    /**
     * 获取所有标签ID
     */
    public static Set<String> getAllTagIds() {
        return Collections.unmodifiableSet(TAG_NAMES.keySet());
    }

    /**
     * 检查是否为必要标签
     */
    public static boolean isEssentialTag(String tagId) {
        return Arrays.asList(ESSENTIAL_TAG_IDS).contains(tagId);
    }

    /**
     * 检查是否为常用显示标签
     */
    public static boolean isCommonDisplayTag(String tagId) {
        return Arrays.asList(COMMON_DISPLAY_TAG_IDS).contains(tagId);
    }

    /**
     * 获取标签的完整信息
     */
    public static TagInfo getTagInfo(String tagId) {
        return new TagInfo(tagId, getTagName(tagId), getTagVR(tagId));
    }

    /**
     * 标签信息类
     */
    public static class TagInfo {
        private final String tagId;
        private final String name;
        private final VR vr;

        public TagInfo(String tagId, String name, VR vr) {
            this.tagId = tagId;
            this.name = name;
            this.vr = vr;
        }

        public String getTagId() {
            return tagId;
        }

        public String getName() {
            return name;
        }

        public VR getVr() {
            return vr;
        }

        @Override
        public String toString() {
            return String.format("TagInfo[tagId=%s, name=%s, vr=%s]", tagId, name, vr);
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;

            TagInfo other = (TagInfo) obj;
            return tagId.equals(other.tagId);
        }

        @Override
        public int hashCode() {
            return tagId.hashCode();
        }
    }
}
