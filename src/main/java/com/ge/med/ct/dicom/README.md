# DICOM模块重构版

## 📋 概述

这是质量保证工具的DICOM模块重构版本，采用全新的架构设计，解决了原有代码的循环依赖、职责不清等问题。

## 🏗️ 架构特点

### ✅ 主要改进
- **消除循环依赖**: 使用关系管理器模式
- **职责分离**: 数据存储、关系管理、查询服务分离
- **功能合并**: 减少重复代码，统一处理逻辑
- **接口优化**: 遵循接口隔离原则
- **代码简化**: 移除冗余功能，提高可读性

### 📁 目录结构
```
src/main/java/com/ge/med/ct/dicom/
├── core/                               # 核心层
│   ├── DicomRelationshipManager.java  # 关系管理器
│   ├── DicomDataStorage.java          # 数据存储管理器  
│   ├── DicomQueryService.java         # 查询服务
│   ├── DicomDataProvider.java         # 数据提供者接口
│   ├── AbstractDicomDataProvider.java # 抽象数据提供者
│   ├── FileSystemDataProvider.java    # 文件系统数据提供者
│   ├── PesiDataProvider.java          # PESI数据提供者
│   ├── DicomDataService.java          # 数据服务接口
│   ├── DicomDataServiceImpl.java      # 数据服务实现
│   ├── DicomDataSource.java           # 数据源接口
│   ├── FileSystemDataSource.java      # 文件系统数据源
│   └── PesiDataSource.java            # PESI数据源
├── model/                              # 数据模型层
│   ├── DicomExam.java                 # 检查模型
│   ├── DicomSeries.java               # 序列模型
│   ├── DicomImage.java                # 图像模型
│   └── DicomTag.java                  # 标签模型
├── tag/                                # 标签处理层
│   ├── DicomTagConstants.java         # 标签常量
│   └── DicomTagProcessor.java         # 统一标签处理器
├── table/                              # 表格转换层
│   └── SimpleTableConverter.java      # 简化表格转换器
└── storage/                            # 存储层
    └── DicomExporter.java             # 数据导出器
```

## 🚀 快速开始

### 1. 文件系统数据加载

```java
// 创建数据服务
DicomDataService service = DicomDataServiceImpl.getInstance();

// 创建文件系统数据源
FileSystemDataSource source = FileSystemDataSource.createDefault("C:/DICOM_DATA");

// 创建进度回调
DicomDataService.ProgressCallback callback = new DicomDataService.ProgressCallback() {
    @Override
    public void onProgress(DicomDataService.ProgressInfo info) {
        System.out.println(info.getMessage() + " - " + info.getPercentage() + "%");
    }
    
    @Override
    public void onError(String error) {
        System.err.println("错误: " + error);
    }
    
    @Override
    public void onComplete() {
        System.out.println("加载完成!");
    }
};

// 加载数据
service.loadDicomData(source, callback);

// 获取数据提供者
DicomDataProvider provider = service.getDataProvider();

// 使用数据
List<DicomExam> exams = provider.getAllExams();
System.out.println("加载了 " + exams.size() + " 个检查");
```

### 2. PESI数据加载

```java
// 创建PESI数据源
PesiDataSource pesiSource = PesiDataSource.createDefault();

// 加载PESI数据
service.loadDicomData(pesiSource, callback);

// 使用数据
DicomDataProvider provider = service.getDataProvider();
List<DicomExam> exams = provider.getAllExams();
```

### 3. 数据查询

```java
// 获取数据提供者
DicomDataProvider provider = service.getDataProvider();

// 基本查询
List<DicomExam> allExams = provider.getAllExams();
List<DicomSeries> seriesForExam = provider.getSeriesForExam("examId");
List<DicomImage> imagesForSeries = provider.getImagesForSeries("seriesId");

// 文件路径查询
String filePath = provider.getImageFilePath("imageId");
DicomImage imageByPath = provider.getImageByFilePath("/path/to/file.dcm");

// 元数据查询
String tagValue = provider.getTagValue("imageId", "(0010,0010)");
Map<String, String> metadata = provider.getImageMetadata("imageId");

// 统计信息
DicomQueryService.DataStats stats = provider.getDataStats();
System.out.println("统计: " + stats);
```

### 4. 表格数据转换

```java
// 转换检查数据为表格
List<DicomExam> exams = provider.getAllExams();
SimpleTableConverter.TableData examTable = SimpleTableConverter.createExamTableData(exams);

// 获取表格数据
List<String> headers = examTable.getHeaders();
List<List<String>> rows = examTable.getRows();

// 转换序列数据
List<DicomSeries> seriesList = provider.getAllSeries();
SimpleTableConverter.TableData seriesTable = SimpleTableConverter.createSeriesTableData(seriesList);

// 转换图像数据
List<DicomImage> images = provider.getAllImages();
SimpleTableConverter.TableData imageTable = SimpleTableConverter.createImageTableData(images);
```

### 5. 数据导出

```java
// 创建导出器
DicomExporter exporter = new DicomExporter();

// 导出检查数据为CSV
String csvFile = exporter.exportToCsv(provider, "exams.csv", DicomExporter.ExportLevel.EXAM);

// 导出序列数据为JSON
String jsonFile = exporter.exportToJson(provider, "series.json", DicomExporter.ExportLevel.SERIES);

// 导出统计信息
String statsFile = exporter.exportStatistics(provider, "stats.json");
```

## 🔧 高级用法

### 1. 自定义数据源配置

```java
// 自定义文件系统数据源
FileSystemDataSource customSource = new FileSystemDataSource("C:/DICOM_DATA");
customSource.setRecursive(true);
customSource.setFileExtensions(Arrays.asList(".dcm", ".dicom"));
customSource.setMaxFiles(1000); // 限制最大文件数

// 自定义PESI数据源
PesiDataSource customPesi = new PesiDataSource("executeQuery.bat");
customPesi.setQueryCommand("searchPatient");
customPesi.addParameter("patientName", "John Doe");
customPesi.setTimeoutSeconds(600);
```

### 2. 标签处理

```java
// 格式化标签值
String formattedDate = DicomTagProcessor.formatDicomDate("20240101");
String formattedTime = DicomTagProcessor.formatDicomTime("143000");
String formattedName = DicomTagProcessor.formatPatientName("Doe^John^M");

// 类型转换
Integer intValue = DicomTagProcessor.getIntegerValue("123");
Float floatValue = DicomTagProcessor.getFloatValue("12.34");
```

### 3. 直接使用核心组件

```java
// 直接使用数据存储
DicomDataStorage storage = new DicomDataStorage();
DicomRelationshipManager relationManager = new DicomRelationshipManager();
DicomQueryService queryService = new DicomQueryService(storage, relationManager);

// 添加数据
DicomExam exam = new DicomExam("exam1");
storage.addExam(exam);

DicomSeries series = new DicomSeries("series1", "exam1");
storage.addSeries(series);
relationManager.addExamSeriesRelation("exam1", "series1");

// 查询数据
List<DicomSeries> seriesForExam = queryService.getSeriesForExam("exam1");
```

## 📊 性能特点

- **内存优化**: 单向依赖设计，减少内存占用
- **查询效率**: 优化的查询逻辑，提高查询速度
- **并发安全**: 使用ConcurrentHashMap确保线程安全
- **资源管理**: 自动资源清理，避免内存泄漏

## 🔄 与原DCM模块的兼容性

### 保持兼容的部分
- 核心接口的方法签名
- 数据模型的基本属性
- 包结构的层次关系

### 主要变化
- 消除了循环依赖
- 简化了类的职责
- 统一了处理逻辑
- 优化了接口设计

## 📝 注意事项

1. **Java版本**: 需要Java 1.8或更高版本
2. **依赖库**: 需要dcm4che3库用于DICOM标签处理
3. **文件权限**: 确保对DICOM文件目录有读取权限
4. **PESI配置**: 确保PESI查询脚本可执行

## 🛠️ 扩展建议

1. **缓存机制**: 在DicomDataStorage中添加缓存提升性能
2. **异步加载**: 在数据提供者中添加异步加载支持
3. **事件通知**: 添加数据变更事件通知机制
4. **插件架构**: 支持自定义数据源插件

## 📞 支持

如有问题或建议，请联系开发团队。
