package com.ge.med.ct.dicom.core;

import com.ge.med.ct.dicom.model.*;
import com.ge.med.ct.dicom.tag.DicomTagConstants;
import com.ge.med.ct.exception.core.DicomException;
import org.dcm4che3.data.VR;

import java.io.File;
import java.util.*;
import java.util.logging.Logger;

/**
 * 文件系统数据提供者（重构版）
 * 从文件系统扫描和加载DICOM文件
 */
public class FileSystemDataProvider extends AbstractDicomDataProvider {
    private static final Logger LOG = Logger.getLogger(FileSystemDataProvider.class.getName());

    private FileSystemDataSource currentSource;

    @Override
    public DataSourceType getDataSourceType() {
        return DataSourceType.FILE_SYSTEM;
    }

    @Override
    protected void doLoadData() throws Exception {
        if (currentSource == null) {
            throw new IllegalStateException("文件系统数据源未设置");
        }

        LOG.info("开始从文件系统加载DICOM数据: " + currentSource.getRootDirectory());

        // 扫描DICOM文件
        List<File> dicomFiles = scanDicomFiles(currentSource);
        LOG.info("扫描到 " + dicomFiles.size() + " 个DICOM文件");

        // 加载文件
        loadDicomFiles(dicomFiles);

        LOG.info("文件系统数据加载完成");
    }

    /**
     * 设置文件系统数据源并加载数据
     */
    public void loadFromFileSystem(FileSystemDataSource source) throws Exception {
        this.currentSource = source;
        loadData();
    }

    // === 私有方法 ===

    /**
     * 扫描DICOM文件
     */
    private List<File> scanDicomFiles(FileSystemDataSource source) {
        List<File> dicomFiles = new ArrayList<>();
        File rootDir = new File(source.getRootDirectory());

        if (!rootDir.exists() || !rootDir.isDirectory()) {
            LOG.warning("根目录不存在或不是目录: " + source.getRootDirectory());
            return dicomFiles;
        }

        scanDirectory(rootDir, source, dicomFiles);

        // 如果设置了最大文件数限制
        if (source.getMaxFiles() > 0 && dicomFiles.size() > source.getMaxFiles()) {
            dicomFiles = dicomFiles.subList(0, source.getMaxFiles());
            LOG.info("限制文件数量为: " + source.getMaxFiles());
        }

        return dicomFiles;
    }

    /**
     * 递归扫描目录
     */
    private void scanDirectory(File directory, FileSystemDataSource source, List<File> dicomFiles) {
        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            if (file.isDirectory() && source.isRecursive()) {
                scanDirectory(file, source, dicomFiles);
            } else if (file.isFile() && isDicomFile(file, source)) {
                dicomFiles.add(file);
            }
        }
    }

    /**
     * 判断是否为DICOM文件
     */
    private boolean isDicomFile(File file, FileSystemDataSource source) {
        // 检查文件扩展名
        if (!source.isValidFileExtension(file.getName())) {
            return false;
        }

        // 检查文件大小（DICOM文件通常大于128字节）
        if (file.length() < 128) {
            return false;
        }

        // 这里可以添加更多的DICOM文件检测逻辑
        // 例如检查DICOM文件头等

        return true;
    }

    /**
     * 加载DICOM文件列表
     */
    private void loadDicomFiles(List<File> dicomFiles) throws DicomException {
        int totalFiles = dicomFiles.size();
        int processedFiles = 0;

        for (File file : dicomFiles) {
            try {
                loadDicomFile(file);
                processedFiles++;

                if (processedFiles % 100 == 0) {
                    LOG.info(String.format("已处理 %d/%d 个文件", processedFiles, totalFiles));
                }
            } catch (Exception e) {
                LOG.warning("加载文件失败: " + file.getAbsolutePath() + ", 错误: " + e.getMessage());
                // 继续处理其他文件
            }
        }

        LOG.info(String.format("文件加载完成: 成功 %d/%d", processedFiles, totalFiles));
    }

    /**
     * 加载单个DICOM文件
     */
    private void loadDicomFile(File file) throws DicomException {
        // 生成唯一ID
        String fileId = generateFileId(file);
        
        // 创建模拟的DICOM数据（实际应用中应该使用DICOM库读取）
        DicomFileInfo fileInfo = createMockDicomData(file, fileId);
        
        // 添加到数据存储
        addDicomData(fileInfo);
    }

    /**
     * 生成文件ID
     */
    private String generateFileId(File file) {
        return "fs_" + Math.abs(file.getAbsolutePath().hashCode());
    }

    /**
     * 创建模拟的DICOM数据
     */
    private DicomFileInfo createMockDicomData(File file, String fileId) throws DicomException {
        // 这里创建模拟数据，实际应用中应该使用DICOM库解析
        String examId = "exam_" + (Math.abs(file.getParent().hashCode()) % 1000);
        String seriesId = "series_" + (Math.abs(file.getParentFile().getName().hashCode()) % 100);
        String imageId = "image_" + fileId;

        // 创建检查
        DicomExam exam = new DicomExam(examId);
        exam.setPatientID("P" + (Math.abs(examId.hashCode()) % 10000));
        exam.setPatientName("Patient_" + exam.getPatientID());
        exam.setStudyDate("20240101");
        exam.setStudyDescription("CT Scan");

        // 创建序列
        DicomSeries series = new DicomSeries(seriesId, examId);
        series.setSeriesNumber(String.valueOf(Math.abs(seriesId.hashCode()) % 100));
        series.setModality("CT");
        series.setSeriesDescription("Axial CT");

        // 创建图像
        DicomImage image = new DicomImage(imageId, seriesId);
        image.setInstanceNumber(String.valueOf(Math.abs(imageId.hashCode()) % 1000));
        image.setRows(512);
        image.setColumns(512);
        image.setFilePath(file.getAbsolutePath());
        image.setFileName(file.getName());
        image.setFileSize(file.length());

        return new DicomFileInfo(exam, series, image);
    }

    /**
     * 添加DICOM数据到存储
     */
    private void addDicomData(DicomFileInfo fileInfo) throws DicomException {
        // 检查检查是否已存在
        if (!storage.containsExam(fileInfo.exam.getId())) {
            addExam(fileInfo.exam);
        }

        // 检查序列是否已存在
        if (!storage.containsSeries(fileInfo.series.getId())) {
            addSeries(fileInfo.series);
        }

        // 添加图像
        addImage(fileInfo.image);
    }

    /**
     * DICOM文件信息包装类
     */
    private static class DicomFileInfo {
        final DicomExam exam;
        final DicomSeries series;
        final DicomImage image;

        DicomFileInfo(DicomExam exam, DicomSeries series, DicomImage image) {
            this.exam = exam;
            this.series = series;
            this.image = image;
        }
    }

    /**
     * 获取当前数据源
     */
    public FileSystemDataSource getCurrentSource() {
        return currentSource;
    }

    /**
     * 设置数据源
     */
    public void setDataSource(FileSystemDataSource source) {
        this.currentSource = source;
    }
}
