package com.ge.med.ct.dicom.core;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * DICOM关系管理器
 * 负责管理检查、序列、图像之间的关系，消除循环依赖
 */
public class DicomRelationshipManager {
    private static final Logger LOG = Logger.getLogger(DicomRelationshipManager.class.getName());

    // 关系映射
    private final Map<String, List<String>> examToSeries = new ConcurrentHashMap<>();
    private final Map<String, List<String>> seriesToImages = new ConcurrentHashMap<>();
    private final Map<String, String> seriesToExam = new ConcurrentHashMap<>();
    private final Map<String, String> imageToSeries = new ConcurrentHashMap<>();

    /**
     * 添加检查-序列关系
     */
    public void addExamSeriesRelation(String examId, String seriesId) {
        if (examId == null || seriesId == null) {
            return;
        }

        examToSeries.computeIfAbsent(examId, k -> new ArrayList<>()).add(seriesId);
        seriesToExam.put(seriesId, examId);
        
        LOG.fine("添加检查-序列关系: " + examId + " -> " + seriesId);
    }

    /**
     * 添加序列-图像关系
     */
    public void addSeriesImageRelation(String seriesId, String imageId) {
        if (seriesId == null || imageId == null) {
            return;
        }

        seriesToImages.computeIfAbsent(seriesId, k -> new ArrayList<>()).add(imageId);
        imageToSeries.put(imageId, seriesId);
        
        LOG.fine("添加序列-图像关系: " + seriesId + " -> " + imageId);
    }

    /**
     * 获取检查下的所有序列ID
     */
    public List<String> getSeriesForExam(String examId) {
        return examToSeries.getOrDefault(examId, Collections.emptyList());
    }

    /**
     * 获取序列下的所有图像ID
     */
    public List<String> getImagesForSeries(String seriesId) {
        return seriesToImages.getOrDefault(seriesId, Collections.emptyList());
    }

    /**
     * 获取序列所属的检查ID
     */
    public String getExamForSeries(String seriesId) {
        return seriesToExam.get(seriesId);
    }

    /**
     * 获取图像所属的序列ID
     */
    public String getSeriesForImage(String imageId) {
        return imageToSeries.get(imageId);
    }

    /**
     * 获取图像所属的检查ID
     */
    public String getExamForImage(String imageId) {
        String seriesId = getSeriesForImage(imageId);
        return seriesId != null ? getExamForSeries(seriesId) : null;
    }

    /**
     * 移除检查及其所有关联关系
     */
    public void removeExam(String examId) {
        List<String> seriesIds = examToSeries.remove(examId);
        if (seriesIds != null) {
            for (String seriesId : seriesIds) {
                removeSeries(seriesId);
            }
        }
        LOG.fine("移除检查及其关联关系: " + examId);
    }

    /**
     * 移除序列及其所有关联关系
     */
    public void removeSeries(String seriesId) {
        // 移除序列-图像关系
        List<String> imageIds = seriesToImages.remove(seriesId);
        if (imageIds != null) {
            for (String imageId : imageIds) {
                imageToSeries.remove(imageId);
            }
        }

        // 移除检查-序列关系
        String examId = seriesToExam.remove(seriesId);
        if (examId != null) {
            List<String> examSeries = examToSeries.get(examId);
            if (examSeries != null) {
                examSeries.remove(seriesId);
                if (examSeries.isEmpty()) {
                    examToSeries.remove(examId);
                }
            }
        }
        
        LOG.fine("移除序列及其关联关系: " + seriesId);
    }

    /**
     * 移除图像关系
     */
    public void removeImage(String imageId) {
        String seriesId = imageToSeries.remove(imageId);
        if (seriesId != null) {
            List<String> seriesImages = seriesToImages.get(seriesId);
            if (seriesImages != null) {
                seriesImages.remove(imageId);
                if (seriesImages.isEmpty()) {
                    seriesToImages.remove(seriesId);
                }
            }
        }
        LOG.fine("移除图像关系: " + imageId);
    }

    /**
     * 获取统计信息
     */
    public RelationshipStats getStats() {
        return new RelationshipStats(
            examToSeries.size(),
            seriesToExam.size(),
            imageToSeries.size()
        );
    }

    /**
     * 清除所有关系
     */
    public void clear() {
        examToSeries.clear();
        seriesToImages.clear();
        seriesToExam.clear();
        imageToSeries.clear();
        LOG.info("清除所有DICOM关系");
    }

    /**
     * 验证关系一致性
     */
    public boolean validateConsistency() {
        // 验证检查-序列关系一致性
        for (Map.Entry<String, List<String>> entry : examToSeries.entrySet()) {
            String examId = entry.getKey();
            for (String seriesId : entry.getValue()) {
                if (!examId.equals(seriesToExam.get(seriesId))) {
                    LOG.warning("检查-序列关系不一致: " + examId + " <-> " + seriesId);
                    return false;
                }
            }
        }

        // 验证序列-图像关系一致性
        for (Map.Entry<String, List<String>> entry : seriesToImages.entrySet()) {
            String seriesId = entry.getKey();
            for (String imageId : entry.getValue()) {
                if (!seriesId.equals(imageToSeries.get(imageId))) {
                    LOG.warning("序列-图像关系不一致: " + seriesId + " <-> " + imageId);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 关系统计信息
     */
    public static class RelationshipStats {
        private final int examCount;
        private final int seriesCount;
        private final int imageCount;

        public RelationshipStats(int examCount, int seriesCount, int imageCount) {
            this.examCount = examCount;
            this.seriesCount = seriesCount;
            this.imageCount = imageCount;
        }

        public int getExamCount() { return examCount; }
        public int getSeriesCount() { return seriesCount; }
        public int getImageCount() { return imageCount; }

        @Override
        public String toString() {
            return String.format("RelationshipStats[exams=%d, series=%d, images=%d]", 
                examCount, seriesCount, imageCount);
        }
    }
}
